# AI Context - Darve Mobile App

## Architecture Patterns

### State Management
- **GetX** for state management (preferred over MobX)
- **ViewModel pattern** with EntityStateWidget for 4-state management (content, empty, loading, error)
- **Result<T> pattern** for repository layer error handling
- **ResultExtensions.toViewModel()** for automatic state management

### Repository Pattern
- All repository methods return `Result<T>` using `ResultHelper.tryCallAsync()`
- Structured error handling with AppError (message, code, metadata)
- Use DioService for HTTP requests with centralized error handling

### Controller Pattern
- Controllers manage page states and business logic
- Use ViewModel<T> for reactive state management
- Derive loading states from ViewModels instead of separate properties
- Initialize controllers with Bindings for proper dependency injection

### Error Handling
- Repository layer: Return Result<T> with structured errors
- Controller layer: Use ResultExtensions.toViewModel() for state management
- UI layer: EntityStateWidget for consistent error/loading/empty states
- ErrorsHandle for toast notifications and debugging

## UI Patterns

### EntityStateWidget Usage
```dart
// ✅ CORRECT: EntityStateWidget observes ViewModel automatically
EntityStateWidget<T>(
  model: controller.someState,
  onRetry: () async => controller.resetState(),
  itemBuilder: (data) => YourWidget(data),
)

// ❌ INCORRECT: Don't wrap in Obx - it's redundant!
Obx(() => EntityStateWidget<T>(...))
```

### Form Validation
- Use Validators utility class for reusable validation logic
- Implement real-time validation with reactive form controllers
- Show validation errors inline with user-friendly messages

### Navigation
- Use AppRoutes constants for route names
- Implement RouteHelper methods for navigation
- Add routes to AppPages with proper bindings and middleware

## Code Standards

### Naming Conventions
- Controllers: `[Feature]Controller` (e.g., `PrivacySettingsController`)
- Pages: `[Feature]Page` (e.g., `ChangePasswordPage`)
- Bindings: `[Feature]Binding` (e.g., `PrivacySettingsBinding`)
- Repositories: `[Domain]Repository` (e.g., `UserRepository`)

### File Organization
```
lib/
├── api/
│   ├── models/
│   └── repositories/
├── services/
│   ├── auth/
│   ├── http/
│   └── providers/
├── ui/
│   ├── settings/
│   │   ├── main/           # Main settings page
│   │   ├── privacy/        # Privacy & security features
│   │   │   ├── main/       # Core privacy settings
│   │   │   ├── password/   # Password management
│   │   │   ├── authentication/ # 2FA, saved logins
│   │   │   └── security/   # Device management, alerts
│   │   ├── contact/        # Contact & support features
│   │   ├── info/           # More info & legal pages
│   │   └── shared/         # Shared settings components
│   ├── [feature]/
│   │   ├── [feature]_controller.dart
│   │   ├── [feature]_page.dart
│   │   └── [feature]_binding.dart
│   ├── components/
│   └── core/
├── utils/
└── routes/
```

### Import Organization
1. Flutter/Dart imports
2. Third-party package imports
3. Local app imports (alphabetical)

## Current Implementation Status

### Settings Module
- ✅ PrivacySettingsController with Result pattern
- ✅ ChangePasswordPage with EntityStateWidget
- ✅ UserRepository with Result<T> methods
- ✅ VerifyEmailController updated to Result pattern
- ✅ Placeholder pages for 2FA, Saved Login, etc.

### Authentication Module
- ✅ AuthService with Result pattern
- ✅ ProfileController with ViewModel pattern
- ✅ Email verification flow

### Pending Features
- Support & Contact features
- More Info pages (Privacy Policy, Terms, etc.)
- Enhanced settings search
- Device management
- 2FA implementation

## Best Practices

### ResultExtensions Pattern
```dart
// Use toViewModel() for automatic state management
final result = await repository.someMethod();
stateVariable.value = result.toViewModel(
  onError: (error) => errorHandler.displayErrorToast(error, 'operation'),
  onSuccess: (data) => {
    // Success logic here
  },
);
```

### Dependency Injection
- Use ServiceProvider for repository access
- Lazy initialization with Get.lazyPut()
- Proper disposal in controller onClose()

### Testing
- Unit tests for controllers focusing on validation and state logic
- Mock repositories for integration testing
- Test error handling and success scenarios

## Common Pitfalls to Avoid

1. **Don't wrap EntityStateWidget in Obx** - it already observes internally
2. **Don't maintain separate loading properties** - derive from ViewModel states
3. **Don't use manual fold() pattern** - use ResultExtensions.toViewModel()
4. **Don't edit package files manually** - use package managers
5. **Don't pass controllers to child widgets** - pass data and wrap parent in Obx

## Development Workflow

1. **Plan first** - Analyze requirements and create detailed implementation plan
2. **Use codebase-retrieval** - Gather context before making changes
3. **Follow patterns** - Use established architecture patterns consistently
4. **Test thoroughly** - Write and run tests for new functionality
5. **Document changes** - Update context and provide clear summaries

This context should be referenced for all development decisions to ensure consistency and quality.

import 'package:darve/utils/errors.dart';
import 'package:darve/utils/http-requests/challenges/getAllReceivedChallenges.dart';
import 'package:darve/utils/interfaces/challenge_model.dart';

Future<List<Challenge>> getMyChallenges() async {
  try {
    var response = await getAllReceivedTaskRequestsForPost();
    
    List<Challenge> challengesForMe = (response as List)
        .map((challenge) => Challenge.fromJson(challenge as Map<String, dynamic>))
        .toList();

    challengesForMe.sort((a, b) => b.createdOn.compareTo(a.createdOn));

    return challengesForMe;
  } catch (error) {
    ErrorsHandle().displayErrorToast(error, "getMyChallenges");
    return [];
  }
}


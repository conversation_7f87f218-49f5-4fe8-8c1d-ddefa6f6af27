import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

// Simple test controllers that focus only on loading state logic
class TestAuthController extends GetxController {
  RxBool isSigningIn = false.obs;
  
  Future<void> signIn() async {
    try {
      isSigningIn.value = true;
      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 100));
    } finally {
      isSigningIn.value = false;
    }
  }
}

class TestRegisterController extends GetxController {
  RxBool isRegistering = false.obs;
  
  Future<void> register() async {
    try {
      isRegistering.value = true;
      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 100));
    } finally {
      isRegistering.value = false;
    }
  }
}

class TestForgotPasswordController extends GetxController {
  RxBool isSendingResetLink = false.obs;
  
  Future<void> forgotPassword() async {
    try {
      isSendingResetLink.value = true;
      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 100));
    } finally {
      isSendingResetLink.value = false;
    }
  }
}

void main() {
  group('Loading States Tests', () {
    
    group('AuthController Loading State', () {
      late TestAuthController controller;

      setUp(() {
        Get.testMode = true;
        controller = TestAuthController();
      });

      tearDown(() {
        Get.reset();
      });

      test('should start with loading state false', () {
        expect(controller.isSigningIn.value, false);
      });

      test('should set loading state to true during signIn', () async {
        // Start the sign in process
        final future = controller.signIn();
        
        // Check that loading state is true
        expect(controller.isSigningIn.value, true);
        
        // Wait for completion
        await future;
        
        // Check that loading state is false after completion
        expect(controller.isSigningIn.value, false);
      });
    });

    group('RegisterController Loading State', () {
      late TestRegisterController controller;

      setUp(() {
        Get.testMode = true;
        controller = TestRegisterController();
      });

      tearDown(() {
        Get.reset();
      });

      test('should start with loading state false', () {
        expect(controller.isRegistering.value, false);
      });

      test('should set loading state to true during register', () async {
        // Start the registration process
        final future = controller.register();
        
        // Check that loading state is true
        expect(controller.isRegistering.value, true);
        
        // Wait for completion
        await future;
        
        // Check that loading state is false after completion
        expect(controller.isRegistering.value, false);
      });
    });

    group('ForgotPasswordController Loading State', () {
      late TestForgotPasswordController controller;

      setUp(() {
        Get.testMode = true;
        controller = TestForgotPasswordController();
      });

      tearDown(() {
        Get.reset();
      });

      test('should start with loading state false', () {
        expect(controller.isSendingResetLink.value, false);
      });

      test('should set loading state to true during forgotPassword', () async {
        // Start the forgot password process
        final future = controller.forgotPassword();
        
        // Check that loading state is true
        expect(controller.isSendingResetLink.value, true);
        
        // Wait for completion
        await future;
        
        // Check that loading state is false after completion
        expect(controller.isSendingResetLink.value, false);
      });
    });
  });
}

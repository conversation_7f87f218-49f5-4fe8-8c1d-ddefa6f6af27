import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

// Simple test controller that focuses only on validation logic
class TestAuthController extends GetxController {
  final username = TextEditingController();
  final password = TextEditingController();

  // Form validation state
  RxList<String> validationErrors = <String>[].obs;

  /// Validates the sign-in form
  /// Returns true if validation passes, false otherwise
  bool validateSignInForm() {
    validationErrors.clear();
    bool isValid = true;

    // Username/Email validation
    if (username.text.trim().isEmpty) {
      isValid = false;
      validationErrors.add("Email or username is required");
    }

    // Password validation
    if (password.text.isEmpty) {
      isValid = false;
      validationErrors.add("Password is required");
    } else if (password.text.length < 6) {
      isValid = false;
      validationErrors.add("Password must be at least 6 characters");
    }

    return isValid;
  }

  /// Checks if a specific validation error exists
  bool hasValidationError(String errorMessage) {
    return validationErrors.contains(errorMessage);
  }

  /// Clears all validation errors
  void clearValidationErrors() {
    validationErrors.clear();
  }
}

void main() {
  group('AuthController Validation Tests', () {
    late TestAuthController controller;

    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;
      controller = TestAuthController();
    });

    tearDown(() {
      Get.reset();
    });

    test('should validate empty username/email', () {
      // Arrange
      controller.username.text = '';
      controller.password.text = 'password123';

      // Act
      bool isValid = controller.validateSignInForm();

      // Assert
      expect(isValid, false);
      expect(controller.hasValidationError("Email or username is required"), true);
    });

    test('should validate empty password', () {
      // Arrange
      controller.username.text = '<EMAIL>';
      controller.password.text = '';

      // Act
      bool isValid = controller.validateSignInForm();

      // Assert
      expect(isValid, false);
      expect(controller.hasValidationError("Password is required"), true);
    });

    test('should validate short password', () {
      // Arrange
      controller.username.text = '<EMAIL>';
      controller.password.text = '123';

      // Act
      bool isValid = controller.validateSignInForm();

      // Assert
      expect(isValid, false);
      expect(controller.hasValidationError("Password must be at least 6 characters"), true);
    });

    test('should pass validation with valid inputs', () {
      // Arrange
      controller.username.text = '<EMAIL>';
      controller.password.text = 'password123';

      // Act
      bool isValid = controller.validateSignInForm();

      // Assert
      expect(isValid, true);
      expect(controller.validationErrors.isEmpty, true);
    });

    test('should clear validation errors', () {
      // Arrange
      controller.username.text = '';
      controller.password.text = '';
      controller.validateSignInForm(); // This will add errors

      // Act
      controller.clearValidationErrors();

      // Assert
      expect(controller.validationErrors.isEmpty, true);
    });
  });
}

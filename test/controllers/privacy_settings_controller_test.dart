import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:darve/ui/settings/privacy/privacy_settings_controller.dart';
import 'package:darve/ui/core/entity_state_widget.dart';

// Note: These tests focus on validation and UI state logic.
// Integration tests with actual API calls would require mocking the UserRepository.

void main() {
  group('PrivacySettingsController Tests', () {
    late PrivacySettingsController controller;

    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;
      controller = PrivacySettingsController();
    });

    tearDown(() {
      controller.dispose();
      Get.reset();
    });

    group('Password Visibility Toggle', () {
      test('should toggle current password visibility', () {
        // Initial state
        expect(controller.isCurrentPasswordVisible.value, false);

        // Toggle visibility
        controller.toggleCurrentPasswordVisibility();
        expect(controller.isCurrentPasswordVisible.value, true);

        // Toggle back
        controller.toggleCurrentPasswordVisibility();
        expect(controller.isCurrentPasswordVisible.value, false);
      });

      test('should toggle new password visibility', () {
        // Initial state
        expect(controller.isNewPasswordVisible.value, false);

        // Toggle visibility
        controller.toggleNewPasswordVisibility();
        expect(controller.isNewPasswordVisible.value, true);

        // Toggle back
        controller.toggleNewPasswordVisibility();
        expect(controller.isNewPasswordVisible.value, false);
      });

      test('should toggle confirm password visibility', () {
        // Initial state
        expect(controller.isConfirmPasswordVisible.value, false);

        // Toggle visibility
        controller.toggleConfirmPasswordVisibility();
        expect(controller.isConfirmPasswordVisible.value, true);

        // Toggle back
        controller.toggleConfirmPasswordVisibility();
        expect(controller.isConfirmPasswordVisible.value, false);
      });
    });

    group('Password Validation', () {
      test('should validate current password correctly', () {
        // Empty password should return error
        expect(controller.validateCurrentPassword(''), 'Current password is required');
        expect(controller.validateCurrentPassword(null), 'Current password is required');

        // Valid password should return null
        expect(controller.validateCurrentPassword('validpassword'), null);
      });

      test('should validate new password correctly', () {
        // Empty password should return error
        expect(controller.validateNewPassword(''), 'Password is required');
        expect(controller.validateNewPassword(null), 'Password is required');

        // Short password should return error
        expect(controller.validateNewPassword('123'), 'Password must be at least 6 characters');

        // Valid password should return null
        expect(controller.validateNewPassword('validpassword'), null);
      });

      test('should validate confirm password correctly', () {
        // Set new password first
        controller.newPasswordController.text = 'newpassword123';

        // Empty confirm password should return error
        expect(controller.validateConfirmPassword(''), 'Please confirm your password');
        expect(controller.validateConfirmPassword(null), 'Please confirm your password');

        // Non-matching password should return error
        expect(controller.validateConfirmPassword('differentpassword'), 'Passwords do not match');

        // Matching password should return null
        expect(controller.validateConfirmPassword('newpassword123'), null);
      });
    });

    group('Form Validation', () {
      test('should check if form is valid correctly', () {
        // Initially form should be invalid
        expect(controller.isFormValid, false);

        // Fill only current password
        controller.currentPasswordController.text = 'currentpass';
        expect(controller.isFormValid, false);

        // Fill current and new password
        controller.newPasswordController.text = 'newpass';
        expect(controller.isFormValid, false);

        // Fill all fields with matching passwords
        controller.confirmPasswordController.text = 'newpass';
        expect(controller.isFormValid, true);

        // Make passwords not match
        controller.confirmPasswordController.text = 'differentpass';
        expect(controller.isFormValid, false);
      });
    });

    group('State Management', () {
      test('should reset change password state correctly', () {
        // Reset state
        controller.resetChangePasswordState();
        expect(controller.changePasswordState.value.state, ViewState.content);
        expect(controller.changePasswordState.value.data, false);
      });

      test('should check loading state correctly', () {
        // Initially not loading
        expect(controller.isLoading, false);
      });
    });
  });
}

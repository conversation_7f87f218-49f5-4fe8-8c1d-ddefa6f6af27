import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

// Simple test controller that doesn't require dependencies
class TestRegisterController extends GetxController {
  RxList<String> errorsArray = <String>[].obs;

  String enteredEmail = '';
  String enteredFullName = '';
  String enteredPassword1 = '';
  String enteredPassword2 = '';

  RxBool isFirstStage = true.obs;

  /// Validates the first stage form (email, name, passwords)
  /// Returns true if validation passes, false otherwise
  bool validateFirstStage() {
    errorsArray.clear(); // Clear previous errors
    bool isValidated = true;

    // Email validation
    if (enteredEmail.isEmpty) {
      isValidated = false;
      errorsArray.add("Email is required");
    } else if (!_isValidEmail(enteredEmail)) {
      isValidated = false;
      errorsArray.add("Please enter a valid email address");
    }

    // Full name validation
    if (enteredFullName.isEmpty || enteredFullName.length <= 3) {
      isValidated = false;
      errorsArray.add("Full Name should be at least 4 characters");
    }

    // Password validation
    if (enteredPassword1.isEmpty || enteredPassword1.length < 8) {
      isValidated = false;
      errorsArray.add("Password must be at least 8 characters");
    }

    // Re-entered password validation
    if (enteredPassword2.isEmpty || enteredPassword2.length < 8) {
      isValidated = false;
      errorsArray.add("Re-entered Password must be at least 8 characters");
    }

    // Password match validation
    if (enteredPassword1 != enteredPassword2) {
      isValidated = false;
      errorsArray.add("Passwords do not match");
    }

    return isValidated;
  }

  /// Proceeds to the next stage if validation passes
  void proceedToNextStage() {
    if (validateFirstStage()) {
      isFirstStage.value = false;
    }
  }

  /// Validates email format using regex
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Checks if a specific error exists in the errors array
  bool hasError(String errorMessage) {
    return errorsArray.contains(errorMessage);
  }
}

void main() {
  group('RegisterController Validation Tests', () {
    late TestRegisterController controller;

    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;
      controller = TestRegisterController();
    });

    tearDown(() {
      Get.reset();
    });

    test('should validate empty email', () {
      // Arrange
      controller.enteredEmail = '';
      controller.enteredFullName = 'John Doe';
      controller.enteredPassword1 = 'password123';
      controller.enteredPassword2 = 'password123';

      // Act
      bool isValid = controller.validateFirstStage();

      // Assert
      expect(isValid, false);
      expect(controller.hasError("Email is required"), true);
    });

    test('should validate invalid email format', () {
      // Arrange
      controller.enteredEmail = 'invalid-email';
      controller.enteredFullName = 'John Doe';
      controller.enteredPassword1 = 'password123';
      controller.enteredPassword2 = 'password123';

      // Act
      bool isValid = controller.validateFirstStage();

      // Assert
      expect(isValid, false);
      expect(controller.hasError("Please enter a valid email address"), true);
    });

    test('should validate short full name', () {
      // Arrange
      controller.enteredEmail = '<EMAIL>';
      controller.enteredFullName = 'Jo';
      controller.enteredPassword1 = 'password123';
      controller.enteredPassword2 = 'password123';

      // Act
      bool isValid = controller.validateFirstStage();

      // Assert
      expect(isValid, false);
      expect(controller.hasError("Full Name should be at least 4 characters"), true);
    });

    test('should validate short password', () {
      // Arrange
      controller.enteredEmail = '<EMAIL>';
      controller.enteredFullName = 'John Doe';
      controller.enteredPassword1 = '123';
      controller.enteredPassword2 = '123';

      // Act
      bool isValid = controller.validateFirstStage();

      // Assert
      expect(isValid, false);
      expect(controller.hasError("Password must be at least 8 characters"), true);
    });

    test('should validate password mismatch', () {
      // Arrange
      controller.enteredEmail = '<EMAIL>';
      controller.enteredFullName = 'John Doe';
      controller.enteredPassword1 = 'password123';
      controller.enteredPassword2 = 'password456';

      // Act
      bool isValid = controller.validateFirstStage();

      // Assert
      expect(isValid, false);
      expect(controller.hasError("Passwords do not match"), true);
    });

    test('should pass validation with valid inputs', () {
      // Arrange
      controller.enteredEmail = '<EMAIL>';
      controller.enteredFullName = 'John Doe';
      controller.enteredPassword1 = 'password123';
      controller.enteredPassword2 = 'password123';

      // Act
      bool isValid = controller.validateFirstStage();

      // Assert
      expect(isValid, true);
      expect(controller.errorsArray.isEmpty, true);
    });

    test('should proceed to next stage with valid inputs', () {
      // Arrange
      controller.enteredEmail = '<EMAIL>';
      controller.enteredFullName = 'John Doe';
      controller.enteredPassword1 = 'password123';
      controller.enteredPassword2 = 'password123';
      controller.isFirstStage.value = true;

      // Act
      controller.proceedToNextStage();

      // Assert
      expect(controller.isFirstStage.value, false);
    });
  });
}

import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

// Simple test controller that focuses only on validation logic
class TestForgotPasswordController extends GetxController {
  String usernameOrEmail = '';

  // Form validation state
  RxList<String> validationErrors = <String>[].obs;

  /// Validates the forgot password form
  /// Returns true if validation passes, false otherwise
  bool validateForgotPasswordForm() {
    validationErrors.clear();
    bool isValid = true;

    // Email/Username validation
    if (usernameOrEmail.trim().isEmpty) {
      isValid = false;
      validationErrors.add("Email or username is required");
    } else if (_isEmail(usernameOrEmail.trim()) && !_isValidEmail(usernameOrEmail.trim())) {
      isValid = false;
      validationErrors.add("Please enter a valid email address");
    }

    return isValid;
  }

  /// Checks if input looks like an email (contains @)
  bool _isEmail(String input) {
    return input.contains('@');
  }

  /// Validates email format using regex
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Checks if a specific validation error exists
  bool hasValidationError(String errorMessage) {
    return validationErrors.contains(errorMessage);
  }

  /// Clears all validation errors
  void clearValidationErrors() {
    validationErrors.clear();
  }
}

void main() {
  group('ForgotPasswordController Validation Tests', () {
    late TestForgotPasswordController controller;

    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;
      controller = TestForgotPasswordController();
    });

    tearDown(() {
      Get.reset();
    });

    test('should validate empty username/email', () {
      // Arrange
      controller.usernameOrEmail = '';

      // Act
      bool isValid = controller.validateForgotPasswordForm();

      // Assert
      expect(isValid, false);
      expect(controller.hasValidationError("Email or username is required"), true);
    });

    test('should validate invalid email format', () {
      // Arrange
      controller.usernameOrEmail = 'invalid@email';

      // Act
      bool isValid = controller.validateForgotPasswordForm();

      // Assert
      expect(isValid, false);
      expect(controller.hasValidationError("Please enter a valid email address"), true);
    });

    test('should pass validation with valid email', () {
      // Arrange
      controller.usernameOrEmail = '<EMAIL>';

      // Act
      bool isValid = controller.validateForgotPasswordForm();

      // Assert
      expect(isValid, true);
      expect(controller.validationErrors.isEmpty, true);
    });

    test('should pass validation with username (no @ symbol)', () {
      // Arrange
      controller.usernameOrEmail = 'testuser';

      // Act
      bool isValid = controller.validateForgotPasswordForm();

      // Assert
      expect(isValid, true);
      expect(controller.validationErrors.isEmpty, true);
    });

    test('should clear validation errors', () {
      // Arrange
      controller.usernameOrEmail = '';
      controller.validateForgotPasswordForm(); // This will add errors

      // Act
      controller.clearValidationErrors();

      // Assert
      expect(controller.validationErrors.isEmpty, true);
    });
  });
}

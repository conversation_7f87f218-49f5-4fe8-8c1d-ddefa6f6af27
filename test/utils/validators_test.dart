import 'package:flutter_test/flutter_test.dart';
import 'package:darve/utils/validators.dart';

void main() {
  group('Validators Tests', () {
    
    group('Email Validation', () {
      test('should validate correct email formats', () {
        expect(Validators.validateEmail('<EMAIL>'), null);
        expect(Validators.validateEmail('<EMAIL>'), null);
        expect(Validators.validateEmail('<EMAIL>'), null);
      });

      test('should reject invalid email formats', () {
        expect(Validators.validateEmail(''), 'Email is required');
        expect(Validators.validateEmail('   '), 'Email is required');
        expect(Validators.validateEmail('invalid-email'), 'Please enter a valid email address');
        expect(Validators.validateEmail('test@'), 'Please enter a valid email address');
        expect(Validators.validateEmail('@domain.com'), 'Please enter a valid email address');
      });
    });

    group('Email or Username Validation', () {
      test('should validate correct email formats', () {
        expect(Validators.validateEmailOrUsername('<EMAIL>'), null);
        expect(Validators.validateEmailOrUsername('<EMAIL>'), null);
      });

      test('should validate correct username formats', () {
        expect(Validators.validateEmailOrUsername('testuser'), null);
        expect(Validators.validateEmailOrUsername('test_user'), null);
        expect(Validators.validateEmailOrUsername('test-user'), null);
        expect(Validators.validateEmailOrUsername('user123'), null);
      });

      test('should reject invalid formats', () {
        expect(Validators.validateEmailOrUsername(''), 'Email or username is required');
        expect(Validators.validateEmailOrUsername('ab'), 'Username must be at least 3 characters');
        expect(Validators.validateEmailOrUsername('a' * 31), 'Username must be less than 30 characters');
        expect(Validators.validateEmailOrUsername('test@invalid'), 'Please enter a valid email address');
      });
    });

    group('Password Validation', () {
      test('should validate correct passwords', () {
        expect(Validators.validatePassword('password123'), null);
        expect(Validators.validatePassword('12345678'), null);
        expect(Validators.validatePassword('a' * 8), null);
      });

      test('should reject invalid passwords', () {
        expect(Validators.validatePassword(''), 'Password is required');
        expect(Validators.validatePassword('1234567'), 'Password must be at least 8 characters');
        expect(Validators.validatePassword('a' * 129), 'Password must be less than 128 characters');
      });
    });

    group('Strong Password Validation', () {
      test('should validate strong passwords', () {
        expect(Validators.validateStrongPassword('Password123'), null);
        expect(Validators.validateStrongPassword('MyPassword1'), null);
        expect(Validators.validateStrongPassword('Test123ABC'), null);
      });

      test('should reject weak passwords', () {
        expect(Validators.validateStrongPassword(''), 'Password is required');
        expect(Validators.validateStrongPassword('password123'), 'Password must contain at least one uppercase letter');
        expect(Validators.validateStrongPassword('PASSWORD123'), 'Password must contain at least one lowercase letter');
        expect(Validators.validateStrongPassword('PasswordABC'), 'Password must contain at least one number');
        expect(Validators.validateStrongPassword('passwordabc'), 'Password must contain at least one uppercase letter');
      });
    });

    group('Password Confirmation Validation', () {
      test('should validate matching passwords', () {
        expect(Validators.validatePasswordConfirmation('password123', 'password123'), null);
        expect(Validators.validatePasswordConfirmation('test', 'test'), null);
      });

      test('should reject non-matching passwords', () {
        expect(Validators.validatePasswordConfirmation('', 'password'), 'Please confirm your password');
        expect(Validators.validatePasswordConfirmation('password1', 'password2'), 'Passwords do not match');
        expect(Validators.validatePasswordConfirmation('test', 'Test'), 'Passwords do not match');
      });
    });

    group('Full Name Validation', () {
      test('should validate correct names', () {
        expect(Validators.validateFullName('John Doe'), null);
        expect(Validators.validateFullName('Mary-Jane Smith'), null);
        expect(Validators.validateFullName("O'Connor"), null);
        expect(Validators.validateFullName('Jean-Pierre'), null);
      });

      test('should reject invalid names', () {
        expect(Validators.validateFullName(''), 'Full name is required');
        expect(Validators.validateFullName('A'), 'Full name must be at least 2 characters');
        expect(Validators.validateFullName('a' * 51), 'Full name must be less than 50 characters');
        expect(Validators.validateFullName('John123'), 'Full name can only contain letters, spaces, hyphens, and apostrophes');
      });
    });

    group('Username Validation', () {
      test('should validate correct usernames', () {
        expect(Validators.validateUsername('testuser'), null);
        expect(Validators.validateUsername('test_user'), null);
        expect(Validators.validateUsername('user123'), null);
        expect(Validators.validateUsername('test-user'), null);
      });

      test('should reject invalid usernames', () {
        expect(Validators.validateUsername(''), 'Username is required');
        expect(Validators.validateUsername('ab'), 'Username must be at least 3 characters');
        expect(Validators.validateUsername('a' * 31), 'Username must be less than 30 characters');
        expect(Validators.validateUsername('_testuser'), 'Username cannot start or end with underscore or hyphen');
        expect(Validators.validateUsername('testuser_'), 'Username cannot start or end with underscore or hyphen');
        expect(Validators.validateUsername('-testuser'), 'Username cannot start or end with underscore or hyphen');
        expect(Validators.validateUsername('testuser-'), 'Username cannot start or end with underscore or hyphen');
        expect(Validators.validateUsername('test user'), 'Username can only contain letters, numbers, underscore, and hyphen');
      });
    });

    group('Phone Number Validation', () {
      test('should validate correct phone numbers', () {
        expect(Validators.validatePhoneNumber('1234567890'), null);
        expect(Validators.validatePhoneNumber('+1234567890'), null);
        expect(Validators.validatePhoneNumber('(*************'), null);
        expect(Validators.validatePhoneNumber('************'), null);
      });

      test('should reject invalid phone numbers', () {
        expect(Validators.validatePhoneNumber(''), 'Phone number is required');
        expect(Validators.validatePhoneNumber('123456789'), 'Phone number must be at least 10 digits');
        expect(Validators.validatePhoneNumber('1' * 16), 'Phone number must be less than 15 digits');
        expect(Validators.validatePhoneNumber('abc1234567'), 'Please enter a valid phone number');
      });
    });

    group('Generic Validators', () {
      test('should validate required fields', () {
        expect(Validators.validateRequired('test'), null);
        expect(Validators.validateRequired(''), 'This field is required');
        expect(Validators.validateRequired('', 'Name'), 'Name is required');
      });

      test('should validate minimum length', () {
        expect(Validators.validateMinLength('test', 4), null);
        expect(Validators.validateMinLength('test', 5), 'This field must be at least 5 characters');
        expect(Validators.validateMinLength('ab', 3, 'Password'), 'Password must be at least 3 characters');
      });

      test('should validate maximum length', () {
        expect(Validators.validateMaxLength('test', 5), null);
        expect(Validators.validateMaxLength('test', 3), 'This field must be less than 3 characters');
        expect(Validators.validateMaxLength('toolong', 5, 'Name'), 'Name must be less than 5 characters');
      });

      test('should validate numeric input', () {
        expect(Validators.validateNumeric('123'), null);
        expect(Validators.validateNumeric('123.45'), null);
        expect(Validators.validateNumeric(''), 'This field is required');
        expect(Validators.validateNumeric('abc'), 'This field must be a valid number');
        expect(Validators.validateNumeric('12abc', 'Age'), 'Age must be a valid number');
      });

      test('should validate URL format', () {
        expect(Validators.validateUrl('https://example.com'), null);
        expect(Validators.validateUrl('http://test.org'), null);
        expect(Validators.validateUrl(''), 'URL is required');
        expect(Validators.validateUrl('invalid-url'), 'Please enter a valid URL starting with http:// or https://');
        expect(Validators.validateUrl('ftp://test.com'), 'Please enter a valid URL starting with http:// or https://');
      });
    });
  });
}

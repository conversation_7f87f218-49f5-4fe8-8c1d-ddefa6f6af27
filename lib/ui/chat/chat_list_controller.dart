import 'dart:async';

import 'package:darve/helpers/discussion_helper.dart';
import 'package:darve/helpers/user_helper.dart';
import 'package:darve/api/models/user_model.dart';
import 'package:darve/api/repositories/chat_repository.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/utils/request_cache.dart';
import 'package:darve/services/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/api/models/chat_list_model.dart';
import 'package:get/get.dart';

class ChatListController extends GetxController {
  final ChatRepository chatListRepository = ServiceProvider.chatRepository;
  late final AuthService authService;
  var chatList = <ChatListModel>[].obs;
  var isLoading = true.obs;
  RequestCache? requestCache;
  RxString searchQuery = ''.obs;

  StreamSubscription? _chatSubscription;

  @override
  void onInit() {
    super.onInit();
    authService = AuthProvider.auth;
  }

  UserModel? get user => authService.user;

  void loadData() {
    isLoading = true.obs;
    searchQuery = ''.obs;
    _chatSubscription ??= chatListRepository.getChatListSse().listen((val) {
      getChats();
    });
    getChats();
  }

  RxList<ChatListModel> get filteredChats => chatList
      .where(
          (chat) => chat.id.toLowerCase().contains(searchQuery.toLowerCase()))
      .toList()
      .obs;

  Future<void> getChats() async {
    requestCache ??= await RequestCache.getInstance();
    var cache = requestCache!.getChatListCache();
    if (cache != null) {
      await parseChats(cache);
    }
    var val = await chatListRepository.getChatsList();
    if (val != null) {
      requestCache!.setChatListCache(val);
      await parseChats(val);
    }
    isLoading.value = false;
  }

  Future<void> parseChats(Map<String, dynamic> responseMap) async {
    List<ChatListModel> localChats = [];
    for (var i = 0; i < responseMap['discussions'].length; i++) {
      var d = responseMap['discussions'][i];
      var userDetails = await ServiceProvider.profileRepository.getProfileData(UserIdHelper()
          .getOtherUserIds(d['chat_room_user_ids'], user!.id)!
          .first);
      if (userDetails.userId != null) {
        localChats.add(ChatListModel(
            id: DiscussionIdHelper().getDiscussionId(d),
            title: d['title'],
            userIds: userDetails.userId!,
            avatarUrl: userDetails.imageUri!,
            fullName: userDetails.fullName!,
            isOnline: false,
            userName: userDetails.userName!,
            lastMessage: d['latest_post']['content'],
            timestamp: '10:30 AM' // TODO: hardcoded
            ));
      }
    }
    chatList.value = localChats;
  }

  @override
  void dispose() {
    _chatSubscription?.cancel();
    super.dispose();
  }
}

import 'package:darve/ui/components/chat/chat_bubble.dart';
import 'package:darve/ui/components/chat/chat_header.dart';
import 'package:darve/ui/components/chat/toggle_challenge.dart';
import 'package:darve/ui/chat/chat_controller.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChatScreen extends StatefulWidget {
  final String chatId;
  final String title;
  final String avatarUrl;
  final String userId;

  const ChatScreen({
    super.key,
    required this.chatId,
    required this.title,
    required this.avatarUrl,
    required this.userId,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  bool isChallengeSelected = false;
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final ChatController chatController =
      Get.put(ChatController(), tag: Unique<PERSON>ey().toString());

  @override
  void initState() {
    super.initState();
    chatController.discussionId = widget.chatId.obs;
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void setIsChallengeSelected(bool val) {
    setState(() {
      isChallengeSelected = val;
    });
  }

  // Scroll to the bottom of the chat
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    });
  }

  Widget getChats() {
    return SizedBox(
      child: Obx(
        () {
          _scrollToBottom();
          return Column(children: [
            if (chatController.chats.isEmpty && !chatController.isLoading.value)
              const Center(
                child: Text("No messages yet"),
              ),
            ...chatController.chats.map<Widget>((message) {
              return message.type == "text"
                  ? ChatBubble(
                      inbound: message.inbound,
                      body: message.body,
                      time: message.time,
                    )
                  : const Placeholder();
            }),
          ]);
        },
      ),
    );
  }

  Widget getChallenges() {
    return const Placeholder();
  }

  Widget getContent() {
    return isChallengeSelected ? getChallenges() : getChats();
  }

  void _sendMessage() {
    if (_messageController.text.isNotEmpty) {
      chatController.postMessageInChat(_messageController.text).then((_) {
        setState(() {
          _messageController.clear();
        });
        _scrollToBottom();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Container(
          color: Colors.white,
          child: Column(
            children: [
              ChatHeader(
                chatId: widget.chatId,
                title: widget.title,
                avatarUrl: widget.avatarUrl,
                userId: widget.userId,
              ),
              ToggleChallenge(
                  isChallengeSelected: isChallengeSelected,
                  onChange: setIsChallengeSelected),
              Obx(
                () {
                  return (chatController.isLoading.value)
                      ? const Center(
                          child: CircularProgressIndicator(),
                        )
                      : SizedBox();
                },
              ),
              Expanded(
                child: SingleChildScrollView(
                  controller: _scrollController,
                  child: getContent(),
                ),
              ),
              if (!isChallengeSelected)
                Padding(
                  padding: const EdgeInsets.only(
                      top: 4.0, bottom: 8.0, right: 16.0, left: 16.0),
                  child: Container(
                    padding: const EdgeInsets.all(4.0),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(100),
                      boxShadow: [
                        BoxShadow(
                          color: Styles.primaryColor.withValues(alpha: .1),
                          spreadRadius: 1,
                          blurRadius: 5,
                        ),
                      ],
                    ),
                    child: GestureDetector(
                      onTap: () => _messageController.selection =
                          TextSelection.fromPosition(TextPosition(
                              offset: _messageController.text.length)),
                      child: TextField(
                        controller: _messageController,
                        onSubmitted: (value) => _sendMessage(),
                        decoration: InputDecoration(
                          hintText: 'Message',
                          hintStyle:
                              const TextStyle(color: Styles.textLightColor),
                          border: InputBorder.none,
                          suffixIcon: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: Styles.primaryColor,
                                  borderRadius: BorderRadius.circular(100),
                                ),
                                child: IconButton(
                                  icon: const Icon(
                                    Icons.send,
                                    color: Colors.white,
                                    size: 18,
                                  ),
                                  onPressed: _sendMessage,
                                ),
                              ),
                            ],
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            vertical: 12.0,
                            horizontal: 20.0,
                          ),
                        ),
                        style: const TextStyle(color: Colors.black),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

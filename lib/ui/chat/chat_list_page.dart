import 'package:darve/ui/components/common/search_field.dart';
import 'package:darve/ui/chat/chat_list_controller.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../components/chat/chat_row.dart';

class ChatListPage extends StatelessWidget {
  ChatListPage({super.key});

  final ChatListController controller = Get.put(ChatListController());

  @override
  Widget build(BuildContext context) {
    controller.loadData();
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Chat',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w800,
            fontSize: 24,
          ),
        ),
      ),
      body: Column(
        children: [
          SearchField(onChanged: (value) {
            controller.searchQuery.value = value;
          }),
          Obx(() {
            if (controller.isLoading.value) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(8.0),
                  child: CircularProgressIndicator(),
                ),
              );
            } else {
              return Container();
            }
          }),
          Obx(() {
            if ((controller.searchQuery.isEmpty &&
                    controller.chatList.isNotEmpty) ||
                (controller.searchQuery.isNotEmpty &&
                    controller.filteredChats.isNotEmpty)) {
              return Expanded(
                child: ListView.builder(
                  itemCount: controller.filteredChats.length,
                  itemBuilder: (context, index) {
                    final chat = controller.filteredChats[index];
                    return ChatRow(
                      chatId: chat.id,
                      title: chat.fullName!,
                      lastMessage: chat.lastMessage!,
                      timestamp: chat.timestamp!,
                      avatarUrl: chat.avatarUrl!,
                      isOnline: chat.isOnline!,
                      userId: chat.userIds!,
                    );
                  },
                ),
              );
            } else if (!controller.isLoading.value) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      Container(
                        margin: const EdgeInsets.only(left: 16, right: 16),
                        child: Divider(
                          color: Colors.grey[200],
                        ),
                      ),
                      const SizedBox(height: 8.0),
                      const Text(
                        "No chats yet!",
                        style: Styles.lightTextStyle,
                      ),
                    ],
                  ),
                ),
              );
            } else {
              return Container();
            }
          }),
        ],
      ),
    );
  }
}

import 'package:darve/utils/show_snackbar.dart';
import 'package:darve/ui/chat/chat_list_controller.dart';
import 'package:darve/ui/notifications/notifications_controller.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/services/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/routes/route_helper.dart';
import 'package:darve/api/models/chat_list_model.dart';
import 'package:darve/api/models/notification.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  late final AuthService authService;
  final ChatListController chatController = Get.put(ChatListController());
  final NotificationsController notificationsController =
      Get.put(NotificationsController());

  @override
  void initState() {
    super.initState();
    authService = AuthProvider.auth;
  }

  Future<List<Widget>> buildList() async {
    List<Widget> widgetsList = [];
    int chatListIdx = 0;
    await chatController.getChats();
    List<ChatListModel> chatList = chatController.chatList;

    for (var i = 0; i < notificationsController.notifications.length; i++) {
      var notif = notificationsController.notifications[i];
      if (notif.type == DarveNotification.userFollowAdded) {
        String username = (notif.value as UserFollowAdded).username;
        final details = await ServiceProvider.profileRepository.getProfileData(username);
        var isFollowingUser =
            await ServiceProvider.profileRepository.isFollowing(details.userId!);
        widgetsList.add(buildFollowActivityTile(username, details.userId!,
            imageUri: details.imageUri!, isFollowing: isFollowingUser));
      }
      if (notif.type == DarveNotification.userFollowAdded &&
          chatList.length > chatListIdx) {
        var chat = chatList[chatListIdx];
        widgetsList.add(buildReceivedMessage(chat.userName!, chat.lastMessage!,
            chat.id, chat.fullName!, chat.userIds!,
            imageUri: chat.avatarUrl!));
        chatListIdx++;
      }
      if (notif.type == DarveNotification.userTaskRequestReceived) {
        // add this later , added type here cause exists in backend so need to handle
      }
      if (notif.type == DarveNotification.userTaskRequestDelivered) {
        // add this later , added type here cause exists in backend so need to handle
      }
      if (notif.type == DarveNotification.userCommunityPost) {
        // add this later , added type here cause exists in backend so need to handle
      }
      if (notif.type == DarveNotification.userTaskRequestCreated) {
        // add this later , added type here cause exists in backend so need to handle
      }
      if (notif.type == DarveNotification.userBalanceUpdate) {
        // add this later , added type here cause exists in backend so need to handle
      }
    }
    return widgetsList.isNotEmpty
        ? widgetsList
        : [
            Container(
              color: Colors.white,
              child: const Center(
                child: Text(
                  "No notifications yet!",
                  style: Styles.lightTextStyle,
                ),
              ),
            )
          ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Notifications',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.black,
            fontSize: 22,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => RouteHelper.goBack(),
        ),
      ),
      body: Container(
        color: Colors.white,
        padding: const EdgeInsets.all(16),
        child: Obx(
          () => notificationsController.notifications.isEmpty
              ? const Center(
                  child: CircularProgressIndicator(
                    color: Styles.primaryColor,
                  ),
                )
              : FutureBuilder<List<Widget>>(
                  future: buildList(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState != ConnectionState.done) {
                      return const Center(
                        child: CircularProgressIndicator(
                          color: Styles.primaryColor,
                        ),
                      );
                    }
                    if (snapshot.hasError) {
                      return Center(
                        child: Text(
                          'Error loading notifications ${snapshot.error}',
                          style: Styles.lightTextStyle,
                        ),
                      );
                    }
                    return Column(
                      children: snapshot.data ?? [],
                    );
                  },
                ),
        ),
      ),
    );
  }

  Widget buildFollowActivityTile(String username, String userId,
      {bool isFollowing = false, required String imageUri}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              RouteHelper.goToProfile(
                userId: userId,
                username: username,
                imageUrl: imageUri,
              );
            },
            child: CircleAvatar(
              radius: 24,
              backgroundImage: NetworkImage(imageUri),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text.rich(
              TextSpan(
                text: '@$username',
                style: const TextStyle(fontWeight: FontWeight.bold),
                children: const [
                  TextSpan(
                    text: ' started following you.',
                    style: TextStyle(fontWeight: FontWeight.normal),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          isFollowing
              ? _blackButton("Following", onClick: () async {
                  await ServiceProvider.profileRepository.unfollowUser(userId);
                  SnackbarHelper.showFollowSnackbar(
                      context: context,
                      imageUri: imageUri,
                      username: username,
                      bgColor: Colors.red,
                      isFollowed: false);
                  setState(() {});
                })
              : _outlineButton("Follow", onClick: () async {
                  await ServiceProvider.profileRepository.followUser(userId);
                  SnackbarHelper.showFollowSnackbar(
                      context: context,
                      imageUri: imageUri,
                      username: username,
                      bgColor: Colors.green,
                      isFollowed: true);
                  setState(() {});
                }),
        ],
      ),
    );
  }

  Widget buildReceivedMessage(String username, String message, String chatId,
      String title, String userId,
      {required String imageUri}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              RouteHelper.goToProfile(
                userId: userId,
                username: username,
                imageUrl: imageUri,
              );
            },
            child: CircleAvatar(
              radius: 24,
              backgroundImage: NetworkImage(imageUri),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text.rich(
              TextSpan(
                text: "@$username",
                style: const TextStyle(fontWeight: FontWeight.bold),
                children: [
                  const TextSpan(
                    text: ' sent a message ',
                    style: TextStyle(fontWeight: FontWeight.normal),
                  ),
                  TextSpan(
                    text: " '$message' ",
                    style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontStyle: FontStyle.italic,
                        fontSize: 12),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          _blackButton("Reply", onClick: () {
            RouteHelper.goToChat(
              chatId: chatId,
              title: title,
              avatarUrl: imageUri,
              userId: userId,
            );
          }),
        ],
      ),
    );
  }

  Widget _blackButton(String text, {Function? onClick}) {
    return GestureDetector(
      onTap: () {
        if (onClick != null) {
          onClick();
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(text, style: const TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget _outlineButton(String text, {Function? onClick}) {
    return GestureDetector(
      onTap: () {
        if (onClick != null) {
          onClick();
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.black),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(text, style: const TextStyle(color: Colors.black)),
      ),
    );
  }
}

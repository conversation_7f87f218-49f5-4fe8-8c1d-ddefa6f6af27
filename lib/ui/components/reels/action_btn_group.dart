import 'package:darve/routes/route_helper.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class ActionBtnGroup extends StatefulWidget {
  const ActionBtnGroup({super.key});

  @override
  State<ActionBtnGroup> createState() => _ActionBtnGroupState();
}

class _ActionBtnGroupState extends State<ActionBtnGroup> {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            color: Colors.white,
          ),
          child: IconButton(
            icon: const Icon(
              Icons.search_outlined,
              color: Styles.textLightColor,
            ),
            onPressed: () {
              RouteHelper.goToSearch();
            },
          ),
        ),
        const SizedBox(width: 8.0),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            color: Colors.white,
          ),
          child: <PERSON><PERSON><PERSON><PERSON><PERSON>(
            icon: const Icon(
              Icons.notifications_outlined,
              color: Styles.textLightColor,
            ),
            onPressed: () {
              RouteHelper.goToNotifications();
            },
          ),
        ),
      ],
    );
  }
}

import 'package:darve/routes/route_helper.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/utils/server_assets.dart';
import 'package:darve/utils/timezone_helper.dart';
import 'package:flutter/material.dart';
import 'package:darve/utils/errors.dart';

class ReelUser extends StatefulWidget {
  final String username;
  final String timestamp;
  const ReelUser(this.username, this.timestamp, {super.key});

  @override
  State<ReelUser> createState() => _ReelUserState();
}

class _ReelUserState extends State<ReelUser> {
  String avatarUrl = '';
  String userId = '';
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _fetchUserData();
  }

  Future<void> _fetchUserData() async {
    try {
      final profileData =
          await ServiceProvider.profileRepository.getProfileData(widget.username);
      setState(() {
        avatarUrl = profileData.imageUri!;
        userId = profileData.userId!;
        isLoading = false;
      });
    } catch (error) {
      ErrorsHandle().displayErrorToast(error, "ReelUser");
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Positioned(
      top: 60,
      left: 20,
      child: Row(
        children: [
          GestureDetector(
            onTap: () async {
              final profileData =
                  await ServiceProvider.profileRepository.getProfileData(widget.username);
              RouteHelper.goToProfile(
                userId: profileData.userId!,
                username: profileData.userName!,
                imageUrl: profileData.imageUri!,
              );
            },
            child: Stack(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.grey,
                    shape: BoxShape.circle,
                    image: avatarUrl.isNotEmpty
                        ? DecorationImage(
                            image: NetworkImage(
                                ServerAssets().getAssetUrl(avatarUrl)),
                            fit: BoxFit.cover,
                          )
                        : null,
                  ),
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: Image.asset('assets/images/home/<USER>'),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 10),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                widget.username,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                TimezoneHelper().timeAgo(widget.timestamp),
                style: const TextStyle(
                  color: Colors.white54,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

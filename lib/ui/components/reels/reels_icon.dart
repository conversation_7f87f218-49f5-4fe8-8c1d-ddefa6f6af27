import 'package:flutter/material.dart';

class ReelsIcon extends StatelessWidget {
  final VoidCallback onTap;
  final String icon;
  final String value;
  const ReelsIcon(this.icon, this.onTap, this.value, {super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
              color: const Color.fromARGB(91, 255, 255, 255),
              borderRadius: BorderRadius.circular(100)),
          child: I<PERSON><PERSON><PERSON><PERSON>(
            icon: Image.asset(
              icon,
              height: 22,
              width: 22,
            ),
            onPressed: onTap,
          ),
        ),
        Text(
          value,
          style:
              const TextStyle(color: Colors.white, fontWeight: FontWeight.w500),
        )
      ],
    );
  }
}

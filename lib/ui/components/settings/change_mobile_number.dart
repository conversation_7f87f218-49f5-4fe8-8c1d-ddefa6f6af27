import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ChangeMobileNumber extends StatelessWidget {
  final TextEditingController _mobileController = TextEditingController();

  ChangeMobileNumber({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          const Text(
            "Phone",
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _mobileController,
            decoration: InputDecoration(
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: const BorderSide(
                  color: Styles.primaryColor, // Your desired color
                  width: 1.0,
                ),
              ),
              hintText: "Enter new mobile number",
              fillColor: const Color(0xFFEEEEEE),
              filled: true,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: BorderSide.none,
              ),
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 15.0, horizontal: 20.0),
            ),
            style: const TextStyle(
              fontWeight: FontWeight.w400,
            ),
            keyboardType: TextInputType.phone,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(
                  10), // Limits the input to 10 digits
            ],
          ),
          const SizedBox(height: 42),
          SizedBox(
            width: MediaQuery.of(context).size.width * 0.4,
            child: ElevatedButton(
              onPressed: () {
                String newMobile = _mobileController.text;
                if (newMobile.isNotEmpty) {
                  Navigator.pop(context, newMobile);
                }
              },
              child: const Text("Save"),
            ),
          ),
          const SizedBox(height: 180),
        ],
      ),
    );
  }
}

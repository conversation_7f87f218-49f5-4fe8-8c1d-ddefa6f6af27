import 'package:darve/ui/components/settings/change_mobile_verification.dart';
import 'package:darve/ui/settings/profile/mobile_verification_controller.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class ChangeMobileNumber extends GetView<MobileVerificationController> {
  const ChangeMobileNumber({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // If verification is completed successfully, show success message
      if (controller.isVerificationCompleted.value) {
        return _buildSuccessState(context);
      }

      // If verification has started, show verification UI
      if (controller.isVerificationStarted.value) {
        return _buildVerificationState(context);
      }

      // Default state: show mobile input
      return _buildMobileInputState(context);
    });
  }

  Widget _buildMobileInputState(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          const Text(
            "Mobile Number",
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
          ),
          const SizedBox(height: 12),
          Obx(() {
            return TextField(
              controller: controller.mobileController,
              decoration: InputDecoration(
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16.0),
                  borderSide: const BorderSide(
                    color: Styles.primaryColor,
                    width: 1.0,
                  ),
                ),
                hintText: "Enter new mobile number",
                fillColor: const Color(0xFFEEEEEE),
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16.0),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                    vertical: 15.0, horizontal: 20.0),
                errorText: controller.errorMessage.value.isEmpty
                    ? null
                    : controller.errorMessage.value,
              ),
              style: const TextStyle(
                fontWeight: FontWeight.w400,
              ),
              keyboardType: TextInputType.phone,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(10),
              ],
            );
          }),
          const SizedBox(height: 16),
          const SizedBox(
            width: double.infinity,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.0),
              child: Text(
                'Re-type Mobile Number',
                style: TextStyle(fontWeight: FontWeight.w400, fontSize: 16),
              ),
            ),
          ),
          const SizedBox(height: 4),
          TextField(
            controller: controller.confirmMobileController,
            decoration: InputDecoration(
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: const BorderSide(
                  color: Styles.primaryColor,
                  width: 1.0,
                ),
              ),
              hintText: "Confirm new mobile number",
              fillColor: const Color(0xFFEEEEEE),
              filled: true,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: BorderSide.none,
              ),
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 15.0, horizontal: 20.0),
              suffixIcon: Obx(() {
                return controller.areMobilesMatching.value
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : const SizedBox.shrink();
              }),
            ),
            style: const TextStyle(
              fontWeight: FontWeight.w400,
            ),
            keyboardType: TextInputType.phone,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(10),
            ],
          ),
          const SizedBox(height: 42),
          Obx(() {
            return SizedBox(
              width: MediaQuery.of(context).size.width * 0.4,
              child: ElevatedButton(
                onPressed: controller.canStartVerification && !controller.isStartingVerification
                    ? controller.startMobileVerification
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: controller.canStartVerification
                      ? Styles.primaryColor
                      : Colors.grey[300],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: controller.isStartingVerification
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Send Code',
                        style: TextStyle(
                          color: controller.canStartVerification ? Colors.white : Colors.grey[600],
                        ),
                      ),
              ),
            );
          }),
          const SizedBox(height: 180),
        ],
      ),
    );
  }

  Widget _buildVerificationState(BuildContext context) {
    return const ChangeMobileVerification();
  }

  Widget _buildSuccessState(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          const Text(
            'Mobile Number Updated',
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
          ),
          const SizedBox(height: 40),
          const Icon(
            Icons.check_circle,
            size: 64,
            color: Colors.green,
          ),
          const SizedBox(height: 16),
          Obx(() {
            return Text(
              'Your mobile number has been successfully updated to\n${controller.mobileController.text}',
              style: const TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            );
          }),
          const SizedBox(height: 24),
          SizedBox(
            width: MediaQuery.of(context).size.width * 0.4,
            child: ElevatedButton(
              onPressed: () {
                // Return the verified mobile number and close modal
                Navigator.pop(context, controller.mobileController.text);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Styles.primaryColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: const Text(
                'Done',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
          const SizedBox(height: 180),
        ],
      ),
    );
  }
}

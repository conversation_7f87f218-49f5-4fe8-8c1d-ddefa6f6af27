import 'dart:io';
import 'package:darve/ui/components/settings/change_date.dart';
import 'package:darve/ui/components/settings/change_email.dart';
import 'package:darve/ui/components/settings/change_full_name.dart';
import 'package:darve/ui/components/settings/change_mobile_number.dart';
import 'package:darve/ui/components/settings/change_mobile_verification.dart';
import 'package:darve/ui/components/settings/change_profile_pic.dart';
import 'package:darve/ui/components/settings/change_username.dart';
import 'package:darve/ui/settings/profile/profile_edit_controller.dart';
import 'package:darve/ui/settings/profile/mobile_verification_controller.dart';
import 'package:darve/ui/settings/profile/components/profile_edit_form.dart';
import 'package:darve/ui/settings/shared/verify_email_controller.dart';
import 'package:darve/utils/server_assets.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileCard extends StatefulWidget {
  final String? avatarUrl;
  final String? name;
  final String? username;

  const ProfileCard({
    super.key,
    required this.avatarUrl,
    required this.name,
    required this.username,
  });

  @override
  State<ProfileCard> createState() => _ProfileCardState();
}

class _ProfileCardState extends State<ProfileCard> {
  int selectedComponentIdx = 0;

  List<String> buttonTitles = [
    "Edit Profile",
    "Full Name",
    "Profile Picture",
    "Username",
    "Date of Birth",
    "Email",
    "Mobile Number"
  ];

  List<Widget> editScreens = [
    const ProfileEditModalContent(),
    ChangeFullName(),
    const ChangeProfilePic(),
    ChangeUsername(),
    const ChangeDate(),
    const ChangeEmail(),
    ChangeMobileNumber()
  ];

  void _showProfileModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      ),
      isScrollControlled: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            void modalHandleClick(int index) {
              setModalState(() {
                selectedComponentIdx = index;
              });
            }

            // VerifyEmailController is now managed by SettingsBinding
            // No need for manual Get.put() or Get.delete() calls

            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(height: 20),
                    if (selectedComponentIdx != 2) ...[
                      CircleAvatar(
                        radius: 32,
                        backgroundImage: widget.avatarUrl != null &&
                                widget.avatarUrl!.isNotEmpty
                            ? NetworkImage(
                                ServerAssets().getAssetUrl(widget.avatarUrl!))
                            : null,
                        child: widget.avatarUrl == null ||
                                widget.avatarUrl!.isEmpty
                            ? const Icon(Icons.person,
                                size: 40, color: Colors.grey)
                            : null,
                      ),
                      const SizedBox(height: 10),
                      Text(
                        widget.name ?? "",
                        style: const TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w400),
                      ),
                      Text(
                        '@${widget.username}',
                        style: const TextStyle(
                            fontSize: 12,
                            color: Styles.textLightColor,
                            fontWeight: FontWeight.w500),
                      ),
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 24),
                        child: Divider(thickness: 1),
                      ),
                    ],
                    if (selectedComponentIdx == 0)
                      const Text(
                        "Profile",
                        style: TextStyle(
                            fontWeight: FontWeight.w600, fontSize: 17),
                      ),
                    const SizedBox(height: 8),
                    if (selectedComponentIdx == 0)
                      ...buttonTitles.asMap().entries.map((entry) {
                        int idx = entry.key;
                        String title = entry.value;
                        return _buildButton(
                            context, title, () => modalHandleClick(idx + 1));
                      }),
                    if (selectedComponentIdx != 0)
                      editScreens[selectedComponentIdx - 1],
                    const SizedBox(height: 80),
                  ],
                ),
              ),
            );
          },
        );
      },
    ).whenComplete(() {
      setState(() {
        selectedComponentIdx = 0;
      });
    });
  }

  Widget _buildButton(
      BuildContext context, String title, VoidCallback handleClick) {
    return Container(
      margin: const EdgeInsets.all(4),
      child: TextButton(
        onPressed: handleClick,
        style: TextButton.styleFrom(
          minimumSize: const Size(double.infinity, 40),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          backgroundColor: Colors.grey[200],
        ),
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                title,
                style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black,
                    fontWeight: FontWeight.w400),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showProfileModal(context),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 30.0, 16.0, 16.0),
        child: Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.topCenter,
          children: [
            Container(
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: .2),
                    offset: const Offset(1, 1),
                    blurRadius: 1,
                    spreadRadius: 1,
                  ),
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: .2),
                    offset: const Offset(-1, 1),
                    blurRadius: 1,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.fromLTRB(24, 40, 24, 10),
                child: Column(
                  children: [
                    Text(
                      widget.name ?? "",
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        color: Colors.black,
                      ),
                    ),
                    Text(
                      '@${widget.username}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.withValues(alpha: .8),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (widget.avatarUrl != null && widget.avatarUrl!.isNotEmpty)
              Positioned(
                top: -32,
                child: CircleAvatar(
                  radius: 32,
                  backgroundColor: Colors.white,
                  backgroundImage: NetworkImage(
                      ServerAssets().getAssetUrl(widget.avatarUrl!)),
                  child: widget.avatarUrl!.isEmpty
                      ? const Icon(Icons.person, size: 50, color: Colors.grey)
                      : null,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Profile Edit Modal Content Widget
/// This widget provides a comprehensive profile editing form within the modal
/// Following the established UI design patterns from existing modal components
class ProfileEditModalContent extends GetView<ProfileEditController> {
  const ProfileEditModalContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading) {
        return _buildLoadingState();
      }

      if (controller.formData == null) {
        return _buildErrorState();
      }

      return _buildEditForm(context);
    });
  }

  Widget _buildLoadingState() {
    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          Text(
            'Edit Profile',
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
          ),
          SizedBox(height: 40),
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Styles.primaryColor),
          ),
          SizedBox(height: 16),
          Text(
            'Loading profile data...',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
          SizedBox(height: 180),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          const Text(
            'Edit Profile',
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
          ),
          const SizedBox(height: 40),
          const Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          const Text(
            'Failed to load profile data',
            style: TextStyle(fontSize: 14, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          SizedBox(
            width: 120,
            child: ElevatedButton(
              onPressed: controller.loadProfileData,
              style: ElevatedButton.styleFrom(
                backgroundColor: Styles.primaryColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: const Text(
                'Retry',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
          const SizedBox(height: 180),
        ],
      ),
    );
  }

  Widget _buildEditForm(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          const Text(
            'Edit Profile',
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
          ),
          const SizedBox(height: 20),

          // Profile Image Section
          _buildProfileImageSection(),

          const SizedBox(height: 20),

          // Full Name Field
          _buildTextField(
            controller: controller.fullNameController,
            hintText: 'Enter your full name',
            errorText: controller.getFieldError('fullName'),
            onChanged: (value) => controller.updateField('fullName', value),
          ),

          const SizedBox(height: 16),

          // Username Field
          _buildTextField(
            controller: controller.usernameController,
            hintText: 'Enter your username',
            errorText: controller.getFieldError('username'),
            onChanged: (value) => controller.updateField('username', value),
            prefixText: '@',
          ),

          const SizedBox(height: 16),

          // Bio Field
          _buildTextField(
            controller: controller.bioController,
            hintText: 'Tell us about yourself',
            errorText: controller.getFieldError('bio'),
            onChanged: (value) => controller.updateField('bio', value),
            maxLines: 2,
          ),

          const SizedBox(height: 24),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: SizedBox(
                  width: MediaQuery.of(context).size.width * 0.35,
                  child: ElevatedButton(
                    onPressed: controller.editEmail,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[200],
                      foregroundColor: Colors.black87,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                    child: const Text('Edit Email'),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: SizedBox(
                  width: MediaQuery.of(context).size.width * 0.35,
                  child: ElevatedButton(
                    onPressed: controller.editMobile,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[200],
                      foregroundColor: Colors.black87,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                    child: const Text('Edit Mobile'),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Save Button
          Obx(() {
            return SizedBox(
              width: MediaQuery.of(context).size.width * 0.4,
              child: ElevatedButton(
                onPressed: controller.hasChanges && !controller.isSaving
                    ? () async {
                        await controller.saveProfile();
                        // Close modal on successful save
                        if (!controller.isSaving) {
                          Navigator.of(context).pop();
                        }
                      }
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: controller.hasChanges
                      ? Styles.primaryColor
                      : Colors.grey[300],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: controller.isSaving
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Save Changes',
                        style: TextStyle(
                          color: controller.hasChanges ? Colors.white : Colors.grey[600],
                        ),
                      ),
              ),
            );
          }),

          const SizedBox(height: 180),
        ],
      ),
    );
  }

  Widget _buildProfileImageSection() {
    return Obx(() {
      return Column(
        children: [
          Stack(
            children: [
              CircleAvatar(
                radius: 40,
                backgroundColor: Colors.grey[200],
                backgroundImage: controller.selectedImagePath.value.isNotEmpty
                    ? FileImage(File(controller.selectedImagePath.value))
                    : (controller.formData?.profileImageUrl?.isNotEmpty == true
                        ? NetworkImage(ServerAssets().getAssetUrl(controller.formData!.profileImageUrl!))
                        : null) as ImageProvider?,
                child: (controller.selectedImagePath.value.isEmpty &&
                       (controller.formData?.profileImageUrl?.isEmpty ?? true))
                    ? const Icon(Icons.person, size: 40, color: Colors.grey)
                    : null,
              ),
              if (controller.isImageLoading.value)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.black.withValues(alpha: 0.5),
                    ),
                    child: const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        strokeWidth: 2,
                      ),
                    ),
                  ),
                ),
              Positioned(
                bottom: 0,
                right: 0,
                child: GestureDetector(
                  onTap: controller.isImageLoading.value ? null : controller.pickProfileImage,
                  child: Container(
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Styles.primaryColor,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 14,
                    ),
                  ),
                ),
              ),
            ],
          ),
          if (controller.selectedImagePath.value.isNotEmpty) ...[
            const SizedBox(height: 8),
            TextButton(
              onPressed: controller.removeSelectedImage,
              child: const Text(
                'Remove Photo',
                style: TextStyle(color: Colors.red, fontSize: 12),
              ),
            ),
          ],
        ],
      );
    });
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String hintText,
    String? errorText,
    Function(String)? onChanged,
    String? prefixText,
    int maxLines = 1,
  }) {
    return TextField(
      controller: controller,
      onChanged: onChanged,
      maxLines: maxLines,
      decoration: InputDecoration(
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16.0),
          borderSide: BorderSide(
            color: errorText != null ? Colors.red : Styles.primaryColor,
            width: 1.0,
          ),
        ),
        hintText: hintText,
        prefixText: prefixText,
        prefixStyle: const TextStyle(
          color: Colors.black87,
          fontWeight: FontWeight.w500,
        ),
        errorText: errorText,
        errorStyle: const TextStyle(
          color: Colors.red,
          fontSize: 12,
        ),
        fillColor: const Color(0xFFEEEEEE),
        filled: true,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16.0),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16.0),
          borderSide: errorText != null
              ? const BorderSide(color: Colors.red, width: 1.0)
              : BorderSide.none,
        ),
        contentPadding: const EdgeInsets.symmetric(
          vertical: 15.0,
          horizontal: 20.0,
        ),
      ),
      style: const TextStyle(
        fontWeight: FontWeight.w400,
      ),
    );
  }
}

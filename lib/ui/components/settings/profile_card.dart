import 'package:darve/ui/components/settings/change_date.dart';
import 'package:darve/ui/components/settings/change_email.dart';
import 'package:darve/ui/components/settings/change_full_name.dart';
import 'package:darve/ui/components/settings/change_mobile_number.dart';
import 'package:darve/ui/components/settings/change_profile_pic.dart';
import 'package:darve/ui/components/settings/change_username.dart';
import 'package:darve/ui/settings/profile/mobile_verification_controller.dart';
import 'package:darve/utils/server_assets.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileCard extends StatefulWidget {
  final String? avatarUrl;
  final String? name;
  final String? username;

  const ProfileCard({
    super.key,
    required this.avatarUrl,
    required this.name,
    required this.username,
  });

  @override
  State<ProfileCard> createState() => _ProfileCardState();
}

class _ProfileCardState extends State<ProfileCard> {
  int selectedComponentIdx = 0;

  List<String> buttonTitles = [
    "Full Name",
    "Profile Picture",
    "Username",
    "Date of Birth",
    "Email",
    "Mobile Number",
    "Delete Account"
  ];

  List<Widget> get editScreens => [
    ChangeFullName(),
    const ChangeProfilePic(),
    ChangeUsername(),
    const ChangeDate(),
    const ChangeEmail(),
    _getMobileNumberWidget(),
    _buildDeleteAccountScreen(),
  ];

  Widget _getMobileNumberWidget() {
    // Reset mobile verification state when opening mobile number modal
    if (Get.isRegistered<MobileVerificationController>()) {
      Get.find<MobileVerificationController>().resetState();
    }
    return const ChangeMobileNumber();
  }

  void _showProfileModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      ),
      isScrollControlled: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            void modalHandleClick(int index) {
              setModalState(() {
                selectedComponentIdx = index;
              });
            }

            // VerifyEmailController is now managed by SettingsBinding
            // No need for manual Get.put() or Get.delete() calls

            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(height: 20),
                    if (selectedComponentIdx != 2) ...[
                      CircleAvatar(
                        radius: 32,
                        backgroundImage: widget.avatarUrl != null &&
                                widget.avatarUrl!.isNotEmpty
                            ? NetworkImage(
                                ServerAssets().getAssetUrl(widget.avatarUrl!))
                            : null,
                        child: widget.avatarUrl == null ||
                                widget.avatarUrl!.isEmpty
                            ? const Icon(Icons.person,
                                size: 40, color: Colors.grey)
                            : null,
                      ),
                      const SizedBox(height: 10),
                      Text(
                        widget.name ?? "",
                        style: const TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w400),
                      ),
                      Text(
                        '@${widget.username}',
                        style: const TextStyle(
                            fontSize: 12,
                            color: Styles.textLightColor,
                            fontWeight: FontWeight.w500),
                      ),
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 24),
                        child: Divider(thickness: 1),
                      ),
                    ],
                    if (selectedComponentIdx == 0)
                      const Text(
                        "Profile",
                        style: TextStyle(
                            fontWeight: FontWeight.w600, fontSize: 17),
                      ),
                    const SizedBox(height: 8),
                    if (selectedComponentIdx == 0)
                      ...buttonTitles.asMap().entries.map((entry) {
                        int idx = entry.key;
                        String title = entry.value;
                        return _buildButton(
                            context, title, () => modalHandleClick(idx + 1));
                      }),
                    if (selectedComponentIdx != 0)
                      editScreens[selectedComponentIdx - 1],
                    const SizedBox(height: 80),
                  ],
                ),
              ),
            );
          },
        );
      },
    ).whenComplete(() {
      setState(() {
        selectedComponentIdx = 0;
      });
    });
  }

  Widget _buildButton(
      BuildContext context, String title, VoidCallback handleClick) {
    // Special styling for Delete Account button
    final isDeleteButton = title == "Delete Account";

    return Container(
      margin: const EdgeInsets.all(4),
      child: TextButton(
        onPressed: handleClick,
        style: TextButton.styleFrom(
          minimumSize: const Size(double.infinity, 40),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          backgroundColor: isDeleteButton ? Colors.red[50] : Colors.grey[200],
          side: isDeleteButton
              ? const BorderSide(color: Colors.red, width: 1)
              : null,
        ),
        child: Row(
          children: [
            if (isDeleteButton)
              const Padding(
                padding: EdgeInsets.only(left: 8.0, right: 12.0),
                child: Icon(
                  Icons.delete_outline,
                  color: Colors.red,
                  size: 20,
                ),
              ),
            Padding(
              padding: EdgeInsets.all(isDeleteButton ? 4.0 : 8.0),
              child: Text(
                title,
                style: TextStyle(
                    fontSize: 16,
                    color: isDeleteButton ? Colors.red : Colors.black,
                    fontWeight: FontWeight.w400),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeleteAccountScreen() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          const Text(
            'Delete Account',
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
          ),
          const SizedBox(height: 20),

          // Warning Icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.red[50],
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.warning_outlined,
              color: Colors.red,
              size: 40,
            ),
          ),

          const SizedBox(height: 20),

          // Warning Text
          const Text(
            'Are you sure you want to delete your account?',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 12),

          const Text(
            'This action cannot be undone. All your data, posts, and connections will be permanently removed.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 32),

          // Delete Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => _showDeleteAccountConfirmation(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text(
                'Delete My Account',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),

          const SizedBox(height: 12),

          // Cancel Button
          SizedBox(
            width: double.infinity,
            child: TextButton(
              onPressed: () {
                // Go back to main profile modal
                setState(() {
                  selectedComponentIdx = 0;
                });
              },
              child: const Text(
                'Cancel',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
              ),
            ),
          ),

          const SizedBox(height: 180),
        ],
      ),
    );
  }

  void _showDeleteAccountConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            'Final Confirmation',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.red,
            ),
          ),
          content: const Text(
            'This is your last chance to cancel. Once confirmed, your account and all data will be permanently deleted and cannot be recovered.',
            style: TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Cancel',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Close profile modal
                // Navigate to verify delete account page
                Get.toNamed('/verify-delete-account');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Delete Forever',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showProfileModal(context),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 30.0, 16.0, 16.0),
        child: Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.topCenter,
          children: [
            Container(
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: .2),
                    offset: const Offset(1, 1),
                    blurRadius: 1,
                    spreadRadius: 1,
                  ),
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: .2),
                    offset: const Offset(-1, 1),
                    blurRadius: 1,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.fromLTRB(24, 40, 24, 10),
                child: Column(
                  children: [
                    Text(
                      widget.name ?? "",
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        color: Colors.black,
                      ),
                    ),
                    Text(
                      '@${widget.username}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.withValues(alpha: .8),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (widget.avatarUrl != null && widget.avatarUrl!.isNotEmpty)
              Positioned(
                top: -32,
                child: CircleAvatar(
                  radius: 32,
                  backgroundColor: Colors.white,
                  backgroundImage: NetworkImage(
                      ServerAssets().getAssetUrl(widget.avatarUrl!)),
                  child: widget.avatarUrl!.isEmpty
                      ? const Icon(Icons.person, size: 50, color: Colors.grey)
                      : null,
                ),
              ),
          ],
        ),
      ),
    );
  }
}




import 'package:darve/ui/components/settings/profile/profile_edit_modal.dart';
import 'package:darve/utils/server_assets.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class ProfileCard extends StatefulWidget {
  final String? avatarUrl;
  final String? name;
  final String? username;

  const ProfileCard({
    super.key,
    required this.avatarUrl,
    required this.name,
    required this.username,
  });

  @override
  State<ProfileCard> createState() => _ProfileCardState();
}

class _ProfileCardState extends State<ProfileCard> {
  int selectedComponentIdx = 0;

  List<String> buttonTitles = [
    "Edit Profile",
    "Full Name",
    "Profile Picture",
    "Username",
    "Date of Birth",
    "Email",
    "Mobile Number"
  ];

  List<Widget> editScreens = [
    const ProfileEditModal(),
    Container(), // Placeholder - now handled by ProfileFieldEditorModal
    Container(), // Placeholder - now handled by ProfileFieldEditorModal
    Container(), // Placeholder - now handled by ProfileFieldEditorModal
    Container(), // Placeholder - now handled by ProfileFieldEditorModal
    Container(), // Placeholder - now handled by ProfileFieldEditorModal
    Container(), // Placeholder - now handled by ProfileFieldEditorModal
  ];

  void _showProfileModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      ),
      isScrollControlled: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            void modalHandleClick(int index) {
              setModalState(() {
                selectedComponentIdx = index;
              });
            }

            // VerifyEmailController is now managed by SettingsBinding
            // No need for manual Get.put() or Get.delete() calls

            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(height: 20),
                    if (selectedComponentIdx != 2) ...[
                      CircleAvatar(
                        radius: 32,
                        backgroundImage: widget.avatarUrl != null &&
                                widget.avatarUrl!.isNotEmpty
                            ? NetworkImage(
                                ServerAssets().getAssetUrl(widget.avatarUrl!))
                            : null,
                        child: widget.avatarUrl == null ||
                                widget.avatarUrl!.isEmpty
                            ? const Icon(Icons.person,
                                size: 40, color: Colors.grey)
                            : null,
                      ),
                      const SizedBox(height: 10),
                      Text(
                        widget.name ?? "",
                        style: const TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w400),
                      ),
                      Text(
                        '@${widget.username}',
                        style: const TextStyle(
                            fontSize: 12,
                            color: Styles.textLightColor,
                            fontWeight: FontWeight.w500),
                      ),
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 24),
                        child: Divider(thickness: 1),
                      ),
                    ],
                    if (selectedComponentIdx == 0)
                      const Text(
                        "Profile",
                        style: TextStyle(
                            fontWeight: FontWeight.w600, fontSize: 17),
                      ),
                    const SizedBox(height: 8),
                    if (selectedComponentIdx == 0)
                      ...buttonTitles.asMap().entries.map((entry) {
                        int idx = entry.key;
                        String title = entry.value;
                        return _buildButton(
                            context, title, () => modalHandleClick(idx + 1));
                      }),
                    if (selectedComponentIdx != 0)
                      editScreens[selectedComponentIdx - 1],
                    const SizedBox(height: 80),
                  ],
                ),
              ),
            );
          },
        );
      },
    ).whenComplete(() {
      setState(() {
        selectedComponentIdx = 0;
      });
    });
  }

  Widget _buildButton(
      BuildContext context, String title, VoidCallback handleClick) {
    return Container(
      margin: const EdgeInsets.all(4),
      child: TextButton(
        onPressed: handleClick,
        style: TextButton.styleFrom(
          minimumSize: const Size(double.infinity, 40),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          backgroundColor: Colors.grey[200],
        ),
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                title,
                style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black,
                    fontWeight: FontWeight.w400),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showProfileModal(context),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 30.0, 16.0, 16.0),
        child: Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.topCenter,
          children: [
            Container(
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: .2),
                    offset: const Offset(1, 1),
                    blurRadius: 1,
                    spreadRadius: 1,
                  ),
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: .2),
                    offset: const Offset(-1, 1),
                    blurRadius: 1,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.fromLTRB(24, 40, 24, 10),
                child: Column(
                  children: [
                    Text(
                      widget.name ?? "",
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        color: Colors.black,
                      ),
                    ),
                    Text(
                      '@${widget.username}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.withValues(alpha: .8),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (widget.avatarUrl != null && widget.avatarUrl!.isNotEmpty)
              Positioned(
                top: -32,
                child: CircleAvatar(
                  radius: 32,
                  backgroundColor: Colors.white,
                  backgroundImage: NetworkImage(
                      ServerAssets().getAssetUrl(widget.avatarUrl!)),
                  child: widget.avatarUrl!.isEmpty
                      ? const Icon(Icons.person, size: 50, color: Colors.grey)
                      : null,
                ),
              ),
          ],
        ),
      ),
    );
  }
}




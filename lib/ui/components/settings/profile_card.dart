import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/ui/components/settings/change_date.dart';
import 'package:darve/ui/components/settings/change_email.dart';
import 'package:darve/ui/components/settings/change_full_name.dart';
import 'package:darve/ui/components/settings/change_mobile_number.dart';
import 'package:darve/ui/components/settings/change_profile_pic.dart';
import 'package:darve/ui/components/settings/change_username.dart';
import 'package:darve/ui/settings/profile/mobile_verification_controller.dart';
import 'package:darve/utils/server_assets.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileCard extends StatefulWidget {
  final String? avatarUrl;
  final String? name;
  final String? username;

  const ProfileCard({
    super.key,
    required this.avatarUrl,
    required this.name,
    required this.username,
  });

  @override
  State<ProfileCard> createState() => _ProfileCardState();
}

class _ProfileCardState extends State<ProfileCard> {
  int selectedComponentIdx = 0;

  List<String> buttonTitles = [
    "Edit Profile",
    "Full Name",
    "Profile Picture",
    "Username",
    "Date of Birth",
    "Email",
    "Mobile Number"
  ];

  List<Widget> editScreens = [
    const ProfileEditModalContent(),
    ChangeFullName(),
    const ChangeProfilePic(),
    ChangeUsername(),
    const ChangeDate(),
    const ChangeEmail(),
    ChangeMobileNumber()
  ];

  void _showProfileModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      ),
      isScrollControlled: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            void modalHandleClick(int index) {
              setModalState(() {
                selectedComponentIdx = index;
              });
            }

            // VerifyEmailController is now managed by SettingsBinding
            // No need for manual Get.put() or Get.delete() calls

            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(height: 20),
                    if (selectedComponentIdx != 2) ...[
                      CircleAvatar(
                        radius: 32,
                        backgroundImage: widget.avatarUrl != null &&
                                widget.avatarUrl!.isNotEmpty
                            ? NetworkImage(
                                ServerAssets().getAssetUrl(widget.avatarUrl!))
                            : null,
                        child: widget.avatarUrl == null ||
                                widget.avatarUrl!.isEmpty
                            ? const Icon(Icons.person,
                                size: 40, color: Colors.grey)
                            : null,
                      ),
                      const SizedBox(height: 10),
                      Text(
                        widget.name ?? "",
                        style: const TextStyle(
                            fontSize: 16, fontWeight: FontWeight.w400),
                      ),
                      Text(
                        '@${widget.username}',
                        style: const TextStyle(
                            fontSize: 12,
                            color: Styles.textLightColor,
                            fontWeight: FontWeight.w500),
                      ),
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 24),
                        child: Divider(thickness: 1),
                      ),
                    ],
                    if (selectedComponentIdx == 0)
                      const Text(
                        "Profile",
                        style: TextStyle(
                            fontWeight: FontWeight.w600, fontSize: 17),
                      ),
                    const SizedBox(height: 8),
                    if (selectedComponentIdx == 0)
                      ...buttonTitles.asMap().entries.map((entry) {
                        int idx = entry.key;
                        String title = entry.value;
                        return _buildButton(
                            context, title, () => modalHandleClick(idx + 1));
                      }),
                    if (selectedComponentIdx != 0)
                      editScreens[selectedComponentIdx - 1],
                    const SizedBox(height: 80),
                  ],
                ),
              ),
            );
          },
        );
      },
    ).whenComplete(() {
      setState(() {
        selectedComponentIdx = 0;
      });
    });
  }

  Widget _buildButton(
      BuildContext context, String title, VoidCallback handleClick) {
    return Container(
      margin: const EdgeInsets.all(4),
      child: TextButton(
        onPressed: handleClick,
        style: TextButton.styleFrom(
          minimumSize: const Size(double.infinity, 40),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          backgroundColor: Colors.grey[200],
        ),
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                title,
                style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black,
                    fontWeight: FontWeight.w400),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showProfileModal(context),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 30.0, 16.0, 16.0),
        child: Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.topCenter,
          children: [
            Container(
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: .2),
                    offset: const Offset(1, 1),
                    blurRadius: 1,
                    spreadRadius: 1,
                  ),
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: .2),
                    offset: const Offset(-1, 1),
                    blurRadius: 1,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.fromLTRB(24, 40, 24, 10),
                child: Column(
                  children: [
                    Text(
                      widget.name ?? "",
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        color: Colors.black,
                      ),
                    ),
                    Text(
                      '@${widget.username}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.withValues(alpha: .8),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (widget.avatarUrl != null && widget.avatarUrl!.isNotEmpty)
              Positioned(
                top: -32,
                child: CircleAvatar(
                  radius: 32,
                  backgroundColor: Colors.white,
                  backgroundImage: NetworkImage(
                      ServerAssets().getAssetUrl(widget.avatarUrl!)),
                  child: widget.avatarUrl!.isEmpty
                      ? const Icon(Icons.person, size: 50, color: Colors.grey)
                      : null,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Profile Edit Modal Content Widget
/// This widget provides a comprehensive profile editing interface within the modal
/// Following the established UI design patterns from existing modal components
class ProfileEditModalContent extends StatelessWidget {
  const ProfileEditModalContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          const Text(
            'Edit Profile',
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
          ),
          const SizedBox(height: 20),

          // Profile Summary
          _buildProfileSummary(),

          const SizedBox(height: 24),

          // Quick Edit Options
          const Text(
            'Quick Edit',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          // Individual Field Edit Buttons
          _buildQuickEditOptions(context),

          const SizedBox(height: 180),
        ],
      ),
    );
  }

  Widget _buildProfileSummary() {
    // Get current user data from AuthProvider
    final authService = Get.find<AuthService>();
    final user = authService.user;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: Colors.grey[200],
            backgroundImage: user?.imageUri?.isNotEmpty == true
                ? NetworkImage(ServerAssets().getAssetUrl(user!.imageUri!))
                : null,
            child: user?.imageUri?.isEmpty ?? true
                ? const Icon(Icons.person, size: 30, color: Colors.grey)
                : null,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user?.name ?? 'No Name',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '@${user?.username ?? 'username'}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                if (user?.bio?.isNotEmpty == true) ...[
                  const SizedBox(height: 4),
                  Text(
                    user!.bio!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickEditOptions(BuildContext context) {
    final List<Map<String, dynamic>> editOptions = [
      {
        'title': 'Full Name',
        'icon': Icons.person_outline,
        'description': 'Update your display name',
        'index': 1, // Corresponds to ChangeFullName in editScreens
      },
      {
        'title': 'Profile Picture',
        'icon': Icons.camera_alt_outlined,
        'description': 'Change your profile photo',
        'index': 2, // Corresponds to ChangeProfilePic in editScreens
      },
      {
        'title': 'Username',
        'icon': Icons.alternate_email,
        'description': 'Update your username',
        'index': 3, // Corresponds to ChangeUsername in editScreens
      },
      {
        'title': 'Date of Birth',
        'icon': Icons.cake_outlined,
        'description': 'Set your birth date',
        'index': 4, // Corresponds to ChangeDate in editScreens
      },
      {
        'title': 'Email',
        'icon': Icons.email_outlined,
        'description': 'Update email address',
        'index': 5, // Corresponds to ChangeEmail in editScreens
      },
      {
        'title': 'Mobile Number',
        'icon': Icons.phone_outlined,
        'description': 'Update phone number',
        'index': 6, // Corresponds to ChangeMobileNumber in editScreens
      },
    ];

    return Column(
      children: editOptions.map((option) {
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                // Navigate to the specific edit modal
                // This will trigger the existing modal system
                Navigator.of(context).pop(); // Close current modal

                // Trigger the profile modal with the specific index
                // This integrates with the existing _showProfileModal system
                _showSpecificEditModal(context, option['index']);
              },
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Styles.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        option['icon'],
                        color: Styles.primaryColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            option['title'],
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            option['description'],
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.chevron_right,
                      color: Colors.grey[400],
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  void _showSpecificEditModal(BuildContext context, int index) {
    // Show the specific edit modal using the existing modal system
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      ),
      isScrollControlled: true,
      builder: (context) {
        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 20),
                // Show the specific edit screen based on index
                _getEditScreen(index),
                const SizedBox(height: 80),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _getEditScreen(int index) {
    switch (index) {
      case 1: return ChangeFullName();
      case 2: return const ChangeProfilePic();
      case 3: return ChangeUsername();
      case 4: return const ChangeDate();
      case 5: return const ChangeEmail();
      case 6:
        // Reset mobile verification state when opening mobile number modal
        if (Get.isRegistered<MobileVerificationController>()) {
          Get.find<MobileVerificationController>().resetState();
        }
        return const ChangeMobileNumber();
      default: return Container();
    }
  }


}

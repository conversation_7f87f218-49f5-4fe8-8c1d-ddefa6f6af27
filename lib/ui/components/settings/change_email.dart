import 'package:darve/ui/settings/shared/verify_email_controller.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';

class ChangeEmail extends GetView<VerifyEmailController> {
  const ChangeEmail({super.key});

  Widget changeEmailWidget(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          const Text(
            'Email',
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
          ),
          const SizedBox(height: 12),
          Obx(() {
            return TextField(
              controller: controller.emailController,
              decoration: InputDecoration(
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16.0),
                  borderSide: const BorderSide(
                    color: Styles.primaryColor, // Your desired color
                    width: 1.0,
                  ),
                ),
                hintText: 'Enter new email',
                fillColor: const Color(0xFFEEEEEE),
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16.0),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                    vertical: 15.0, horizontal: 20.0),
                errorText: controller.errorMessage.value.isEmpty
                    ? null
                    : controller.errorMessage.value,
              ),
              style: const TextStyle(
                fontWeight: FontWeight.w400,
              ),
              keyboardType: TextInputType.emailAddress,
            );
          }),
          const SizedBox(height: 16),
          const SizedBox(
            width: double.infinity,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.0),
              child: Text(
                'Re-type Email',
                style: TextStyle(fontWeight: FontWeight.w400, fontSize: 16),
              ),
            ),
          ),
          const SizedBox(height: 4),
          TextField(
            controller: controller.confirmEmailController,
            decoration: InputDecoration(
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: const BorderSide(
                  color: Styles.primaryColor, // Your desired color
                  width: 1.0,
                ),
              ),
              hintText: 'Confirm new email',
              fillColor: const Color(0xFFEEEEEE),
              filled: true,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: BorderSide.none,
              ),
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 15.0, horizontal: 20.0),
            ),
            style: const TextStyle(
              fontWeight: FontWeight.w400,
            ),
            keyboardType: TextInputType.emailAddress,
          ),
          const SizedBox(height: 42),
          SizedBox(
            width: MediaQuery.of(context).size.width * 0.4,
            child: ElevatedButton(
              onPressed: () {
                controller.verificationStart();
              },
              child: const Text('Save'),
            ),
          ),
          const SizedBox(height: 180),
        ],
      ),
    );
  }

  Widget verifyEmailWidget(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          const Text(
            'Verify your email',
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
          ),
          const SizedBox(height: 12),
          Text(
            'Please enter the 6 digit code sent to\n${controller.emailController.text}',
            style: const TextStyle(fontSize: 14, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Pinput(
            // controller: pinController,
            length: 6,
            onChanged: (value) {
              controller.verificationCode.value = value;
            },
            defaultPinTheme: PinTheme(
              width: 48,
              height: 56,
              textStyle: const TextStyle(
                fontSize: 22,
                color: Colors.black,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: const Color(0xFF3213F1).withAlpha((255 / 10).toInt()),
              ),
            ),
            keyboardType: TextInputType.number,
            onCompleted: (pin) {
              controller.verificationConfirm();
            },
          ),
          const SizedBox(height: 42),
          SizedBox(
            width: MediaQuery.of(context).size.width * 0.4,
            child: ElevatedButton(
              onPressed: () {
                // Handle verification logic here
                Navigator.pop(context);
              },
              child: const Text('Verify'),
            ),
          ),
          const SizedBox(height: 180),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isVerificationCodeValid.value) {
        if (controller.isEmailChangedSuccess.value) {
          return const Center(
            child: SizedBox(
              height: 100,
              child: Text(
                'Email changed successfully!',
                style: TextStyle(fontSize: 18, color: Colors.green),
              ),
            ),
          );
        }
        return changeEmailWidget(context);
      }
      if (controller.isVerificationCodeSent.value) {
        return verifyEmailWidget(context);
      }
      return changeEmailWidget(context);
    });
  }
}

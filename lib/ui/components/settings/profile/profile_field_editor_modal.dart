import 'package:darve/ui/components/settings/change_date.dart';
import 'package:darve/ui/components/settings/change_email.dart';
import 'package:darve/ui/components/settings/change_full_name.dart';
import 'package:darve/ui/components/settings/change_mobile_number.dart';
import 'package:darve/ui/components/settings/change_profile_pic.dart';
import 'package:darve/ui/components/settings/change_username.dart';
import 'package:darve/ui/settings/profile/mobile_verification_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Enum for different profile field types
enum ProfileFieldType {
  fullName,
  profilePicture,
  username,
  dateOfBirth,
  email,
  mobileNumber,
}

/// Profile Field Editor Modal
/// A reusable modal component for editing individual profile fields
/// Follows the established modal design patterns
class ProfileFieldEditorModal extends StatelessWidget {
  final ProfileFieldType fieldType;

  const ProfileFieldEditorModal({
    super.key,
    required this.fieldType,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 20),
            _getFieldEditor(),
            const SizedBox(height: 80),
          ],
        ),
      ),
    );
  }

  Widget _getFieldEditor() {
    switch (fieldType) {
      case ProfileFieldType.fullName:
        return ChangeFullName();
      case ProfileFieldType.profilePicture:
        return const ChangeProfilePic();
      case ProfileFieldType.username:
        return ChangeUsername();
      case ProfileFieldType.dateOfBirth:
        return const ChangeDate();
      case ProfileFieldType.email:
        return const ChangeEmail();
      case ProfileFieldType.mobileNumber:
        // Reset mobile verification state when opening mobile number modal
        if (Get.isRegistered<MobileVerificationController>()) {
          Get.find<MobileVerificationController>().resetState();
        }
        return const ChangeMobileNumber();
    }
  }



  /// Static method to show the field editor modal
  static void show({
    required BuildContext context,
    required ProfileFieldType fieldType,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      ),
      isScrollControlled: true,
      builder: (context) {
        return ProfileFieldEditorModal(fieldType: fieldType);
      },
    );
  }

  /// Static method to show field editor with result callback
  static Future<T?> showWithResult<T>({
    required BuildContext context,
    required ProfileFieldType fieldType,
  }) async {
    return await showModalBottomSheet<T>(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      ),
      isScrollControlled: true,
      builder: (context) {
        return ProfileFieldEditorModal(fieldType: fieldType);
      },
    );
  }

  /// Helper method to get field description
  static String getFieldDescription(ProfileFieldType fieldType) {
    switch (fieldType) {
      case ProfileFieldType.fullName:
        return 'Update your display name';
      case ProfileFieldType.profilePicture:
        return 'Change your profile photo';
      case ProfileFieldType.username:
        return 'Update your username';
      case ProfileFieldType.dateOfBirth:
        return 'Set your birth date';
      case ProfileFieldType.email:
        return 'Update email address';
      case ProfileFieldType.mobileNumber:
        return 'Update phone number';
    }
  }

  /// Helper method to get field icon
  static IconData getFieldIcon(ProfileFieldType fieldType) {
    switch (fieldType) {
      case ProfileFieldType.fullName:
        return Icons.person_outline;
      case ProfileFieldType.profilePicture:
        return Icons.camera_alt_outlined;
      case ProfileFieldType.username:
        return Icons.alternate_email;
      case ProfileFieldType.dateOfBirth:
        return Icons.cake_outlined;
      case ProfileFieldType.email:
        return Icons.email_outlined;
      case ProfileFieldType.mobileNumber:
        return Icons.phone_outlined;
    }
  }
}

import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/ui/components/settings/profile/profile_field_editor_modal.dart';
import 'package:darve/utils/server_assets.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Profile Edit Modal Content Widget
/// This widget provides a comprehensive profile editing interface within the modal
/// Following the established UI design patterns from existing modal components
class ProfileEditModal extends StatelessWidget {
  const ProfileEditModal({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          const Text(
            'Edit Profile',
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
          ),
          const SizedBox(height: 20),
          
          // Profile Summary
          _buildProfileSummary(),
          
          const SizedBox(height: 24),
          
          // Quick Edit Options
          const Text(
            'Quick Edit',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          
          // Individual Field Edit Buttons
          _buildQuickEditOptions(context),

          const SizedBox(height: 32),

          // Delete Account Button
          _buildDeleteAccountButton(context),

          const SizedBox(height: 180),
        ],
      ),
    );
  }

  Widget _buildProfileSummary() {
    // Get current user data from AuthProvider
    final authService = Get.find<AuthService>();
    final user = authService.user;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: Colors.grey[200],
            backgroundImage: user?.imageUri?.isNotEmpty == true
                ? NetworkImage(ServerAssets().getAssetUrl(user!.imageUri!))
                : null,
            child: user?.imageUri?.isEmpty ?? true
                ? const Icon(Icons.person, size: 30, color: Colors.grey)
                : null,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user?.name ?? 'No Name',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '@${user?.username ?? 'username'}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                if (user?.bio?.isNotEmpty == true) ...[
                  const SizedBox(height: 4),
                  Text(
                    user!.bio!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickEditOptions(BuildContext context) {
    final List<Map<String, dynamic>> editOptions = [
      {
        'title': 'Full Name',
        'icon': Icons.person_outline,
        'description': 'Update your display name',
        'fieldType': ProfileFieldType.fullName,
      },
      {
        'title': 'Profile Picture',
        'icon': Icons.camera_alt_outlined,
        'description': 'Change your profile photo',
        'fieldType': ProfileFieldType.profilePicture,
      },
      {
        'title': 'Username',
        'icon': Icons.alternate_email,
        'description': 'Update your username',
        'fieldType': ProfileFieldType.username,
      },
      {
        'title': 'Date of Birth',
        'icon': Icons.cake_outlined,
        'description': 'Set your birth date',
        'fieldType': ProfileFieldType.dateOfBirth,
      },
      {
        'title': 'Email',
        'icon': Icons.email_outlined,
        'description': 'Update email address',
        'fieldType': ProfileFieldType.email,
      },
      {
        'title': 'Mobile Number',
        'icon': Icons.phone_outlined,
        'description': 'Update phone number',
        'fieldType': ProfileFieldType.mobileNumber,
      },
    ];

    return Column(
      children: editOptions.map((option) {
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                // Show the specific field editor modal
                ProfileFieldEditorModal.show(
                  context: context,
                  fieldType: option['fieldType'],
                );
              },
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Styles.primaryColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        option['icon'],
                        color: Styles.primaryColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            option['title'],
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            option['description'],
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.chevron_right,
                      color: Colors.grey[400],
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDeleteAccountButton(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: OutlinedButton.icon(
        onPressed: () => _showDeleteAccountConfirmation(context),
        icon: const Icon(
          Icons.delete_outline,
          color: Colors.red,
          size: 20,
        ),
        label: const Text(
          'Delete Account',
          style: TextStyle(
            color: Colors.red,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        style: OutlinedButton.styleFrom(
          side: const BorderSide(color: Colors.red, width: 1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }

  void _showDeleteAccountConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            'Delete Account',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.red,
            ),
          ),
          content: const Text(
            'Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently removed.',
            style: TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Cancel',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Close profile modal
                // Navigate to verify delete account page
                Get.toNamed('/verify-delete-account');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Delete',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Static method to show the profile edit modal
  static void show(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
      ),
      isScrollControlled: true,
      builder: (context) {
        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 20),
                const ProfileEditModal(),
                const SizedBox(height: 80),
              ],
            ),
          ),
        );
      },
    );
  }
}

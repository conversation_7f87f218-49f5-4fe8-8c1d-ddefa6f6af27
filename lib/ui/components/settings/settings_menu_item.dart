import 'package:flutter/material.dart';

class SettingsMenuItem extends StatelessWidget {
  final String? assetPath;
  final String text;
  final VoidCallback onTap;

  const SettingsMenuItem({
    super.key,
    this.assetPath,
    required this.text,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 50,
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          decoration: BoxDecoration(
            color: const Color.fromRGBO(238, 238, 238, 1),
            borderRadius: BorderRadius.circular(9),
            border: const Border(
              bottom: BorderSide(
                color: Colors.black26,
                width: 1.5,
              ),
            ),
          ),
          child: Row(
            children: [
              if (assetPath != null) ...[
                Image.asset(
                  assetPath!,
                  color: Colors.black,
                  height: 24,
                ),
                const SizedBox(width: 16),
              ],
              Expanded(
                child: Text(
                  text,
                  style: const TextStyle(
                    color: Colors.black87,
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              const Icon(
                Icons.chevron_right,
                color: Color.fromARGB(143, 98, 98, 98),
                size: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:darve/ui/settings/profile/mobile_verification_controller.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';

class ChangeMobileVerification extends GetView<MobileVerificationController> {
  const ChangeMobileVerification({super.key});

  Widget changeMobileWidget(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          const Text(
            'Mobile Number',
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
          ),
          const SizedBox(height: 12),
          Obx(() {
            return TextField(
              controller: controller.mobileController,
              decoration: InputDecoration(
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16.0),
                  borderSide: const BorderSide(
                    color: Styles.primaryColor,
                    width: 1.0,
                  ),
                ),
                hintText: 'Enter new mobile number',
                fillColor: const Color(0xFFEEEEEE),
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16.0),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                    vertical: 15.0, horizontal: 20.0),
                errorText: controller.errorMessage.value.isEmpty
                    ? null
                    : controller.errorMessage.value,
              ),
              style: const TextStyle(
                fontWeight: FontWeight.w400,
              ),
              keyboardType: TextInputType.phone,
            );
          }),
          const SizedBox(height: 16),
          const SizedBox(
            width: double.infinity,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.0),
              child: Text(
                'Re-type Mobile Number',
                style: TextStyle(fontWeight: FontWeight.w400, fontSize: 16),
              ),
            ),
          ),
          const SizedBox(height: 4),
          TextField(
            controller: controller.confirmMobileController,
            decoration: InputDecoration(
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: const BorderSide(
                  color: Styles.primaryColor,
                  width: 1.0,
                ),
              ),
              hintText: 'Confirm new mobile number',
              fillColor: const Color(0xFFEEEEEE),
              filled: true,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: BorderSide.none,
              ),
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 15.0, horizontal: 20.0),
              suffixIcon: Obx(() {
                return controller.areMobilesMatching.value
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : const SizedBox.shrink();
              }),
            ),
            style: const TextStyle(
              fontWeight: FontWeight.w400,
            ),
            keyboardType: TextInputType.phone,
          ),
          const SizedBox(height: 42),
          Obx(() {
            return SizedBox(
              width: MediaQuery.of(context).size.width * 0.4,
              child: ElevatedButton(
                onPressed: controller.canStartVerification && !controller.isStartingVerification
                    ? controller.startMobileVerification
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: controller.canStartVerification
                      ? Styles.primaryColor
                      : Colors.grey[300],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: controller.isStartingVerification
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Send Code',
                        style: TextStyle(
                          color: controller.canStartVerification ? Colors.white : Colors.grey[600],
                        ),
                      ),
              ),
            );
          }),
          const SizedBox(height: 180),
        ],
      ),
    );
  }

  Widget verifyMobileWidget(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          const Text(
            'Verify your mobile',
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
          ),
          const SizedBox(height: 12),
          Obx(() {
            return Text(
              'Please enter the 6 digit code sent to\n${controller.mobileController.text}',
              style: const TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            );
          }),
          const SizedBox(height: 12),
          Pinput(
            length: 6,
            onChanged: (value) {
              controller.updateVerificationCode(value);
            },
            defaultPinTheme: PinTheme(
              width: 48,
              height: 56,
              textStyle: const TextStyle(
                fontSize: 22,
                color: Colors.black,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: const Color(0xFF3213F1).withAlpha((255 / 10).toInt()),
              ),
            ),
            keyboardType: TextInputType.number,
            onCompleted: (pin) {
              controller.confirmMobileVerification();
            },
          ),
          const SizedBox(height: 16),
          Obx(() {
            if (controller.errorMessage.value.isNotEmpty) {
              return Text(
                controller.errorMessage.value,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              );
            }
            return const SizedBox.shrink();
          }),
          const SizedBox(height: 16),
          TextButton(
            onPressed: controller.startMobileVerification,
            child: const Text(
              'Resend Code',
              style: TextStyle(
                color: Styles.primaryColor,
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Obx(() {
            return SizedBox(
              width: MediaQuery.of(context).size.width * 0.4,
              child: ElevatedButton(
                onPressed: controller.canConfirmVerification && !controller.isConfirmingVerification
                    ? () async {
                        await controller.confirmMobileVerification();
                        // The controller will handle navigation back with result
                      }
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: controller.canConfirmVerification
                      ? Styles.primaryColor
                      : Colors.grey[300],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                child: controller.isConfirmingVerification
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Verify',
                        style: TextStyle(
                          color: controller.canConfirmVerification ? Colors.white : Colors.grey[600],
                        ),
                      ),
              ),
            );
          }),
          const SizedBox(height: 180),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isVerificationStarted) {
        return verifyMobileWidget(context);
      }
      return changeMobileWidget(context);
    });
  }
}

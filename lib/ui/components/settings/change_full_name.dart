import 'dart:convert';

import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class ChangeFullName extends StatelessWidget {
  final TextEditingController _nameController = TextEditingController();

  ChangeFullName({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          const Text(
            "Full Name",
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 17),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _nameController,
            decoration: InputDecoration(
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: const BorderSide(
                  color: Styles.primaryColor, // Your desired color
                  width: 1.0,
                ),
              ),
              hintText: "Enter your full name",
              fillColor: const Color(0xFFEEEEEE),
              filled: true,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16.0),
                borderSide: BorderSide.none,
              ),
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 15.0, horizontal: 20.0),
            ),
            style: const TextStyle(
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 42),
          SizedBox(
            width: MediaQuery.of(context).size.width * 0.4,
            child: ElevatedButton(
              onPressed: () async {
                String newName = _nameController.text;
                if (newName.isNotEmpty) {
                  Navigator.pop(context, newName);
                  await ServiceProvider.profileRepository.editProfile(
                      json.encode({
                        "full_name": newName,
                      }),
                      "");
                }
              },
              child: const Text("Save"),
            ),
          ),
          const SizedBox(height: 180),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';

class RecordingIcon extends StatelessWidget {
  final double? topSize;
  const RecordingIcon({super.key, this.topSize});

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          width: (topSize != null) ? topSize : 20,
          height: (topSize != null) ? topSize : 20,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
          ),
        ),
        Container(
          width: (topSize != null) ? (topSize! / 2) : 10,
          height: (topSize != null) ? (topSize! / 2) : 10,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white,
          ),
        ),
      ],
    );
  }
}

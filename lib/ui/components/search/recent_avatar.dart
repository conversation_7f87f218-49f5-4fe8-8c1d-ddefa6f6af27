import 'package:darve/utils/server_assets.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class RecentAvatar extends StatelessWidget {
  const RecentAvatar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      decoration: BoxDecoration(
        border: Border.all(color: Styles.primaryColor, width: 2.0),
        borderRadius: BorderRadius.circular(100),
      ),
      padding: const EdgeInsets.all(4),
      child: CircleAvatar(
        radius: 24,
        backgroundImage: NetworkImage(ServerAssets().getAssetUrl(
            "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR_MyGTnKd3sOo8ote4UvGBC0T5YOcUYjTNyg&s" //hardcoded
            )),
        child: null,
      ),
    );
  }
}

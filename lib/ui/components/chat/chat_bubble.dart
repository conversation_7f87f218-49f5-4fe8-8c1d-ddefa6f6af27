import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class ChatBubble extends StatelessWidget {
  final bool inbound;
  final String body;
  final int time;

  const ChatBubble({
    super.key,
    required this.inbound,
    required this.body,
    required this.time,
  });

  @override
  Widget build(BuildContext context) {
    // Convert the timestamp (milliseconds since epoch) to a DateTime object and format the time
    final timeString = DateTime.fromMillisecondsSinceEpoch(time)
        .toLocal()
        .toString()
        .split(' ')[1]
        .substring(0, 5); // Get only the time (HH:MM)

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      child: Align(
        alignment: inbound ? Alignment.centerLeft : Alignment.centerRight,
        child: Column(
          crossAxisAlignment:
              inbound ? CrossAxisAlignment.start : CrossAxisAlignment.end,
          children: [
            ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.7,
              ),
              child: Container(
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color: inbound
                      ? Styles.chatBubbleLightBgColor
                      : Styles.primaryColor,
                  borderRadius: BorderRadius.only(
                    topLeft: const Radius.circular(100),
                    bottomLeft: inbound
                        ? const Radius.circular(0)
                        : const Radius.circular(100),
                    bottomRight: !inbound
                        ? const Radius.circular(0)
                        : const Radius.circular(100),
                    topRight: const Radius.circular(100),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: Text(
                    body,
                    style:
                        TextStyle(color: inbound ? Colors.black : Colors.white),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 4.0),
            Text(timeString,
                style: const TextStyle(
                    color: Styles.textLightColor, fontSize: 10)),
          ],
        ),
      ),
    );
  }
}

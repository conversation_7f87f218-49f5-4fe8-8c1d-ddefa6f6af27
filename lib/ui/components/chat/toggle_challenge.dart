import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class ToggleChallenge extends StatelessWidget {
  final bool isChallengeSelected;
  final void Function(bool) onChange;
  const ToggleChallenge(
      {super.key, required this.isChallengeSelected, required this.onChange});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          GestureDetector(
              onTap: () {
                onChange(true);
              },
              child: Text(
                "Challenge",
                style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: isChallengeSelected
                        ? Styles.primaryColor
                        : Styles.textLightColor),
              )),
          const SizedBox(width: 16),
          Container(width: 1, height: 20, color: Styles.textLightColor),
          const SizedBox(width: 16),
          GestureDetector(
              onTap: () {
                onChange(false);
              },
              child: Text(
                "Chat",
                style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: !isChallengeSelected
                        ? Styles.primaryColor
                        : Styles.textLightColor),
              )),
        ],
      ),
    );
  }
}

import 'package:darve/routes/route_helper.dart';
import 'package:darve/utils/server_assets.dart';
import 'package:flutter/material.dart';
import '../../../utils/styles.dart'; // Import styles

class ChatRow extends StatelessWidget {
  final String chatId;
  final String userId;
  final String title;
  final String lastMessage;
  final String timestamp;
  final String avatarUrl;
  final bool isOnline;

  const ChatRow({
    super.key,
    required this.chatId,
    required this.title,
    required this.lastMessage,
    required this.timestamp,
    required this.avatarUrl,
    required this.isOnline,
    required this.userId,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: ListTile(
        onTap: () {
          RouteHelper.goToChat(
            chatId: chatId,
            title: title,
            avatarUrl: avatarUrl,
            userId: userId,
          );
        },
        leading: Stack(
          children: [
            Container(
              padding: const EdgeInsets.all(3),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Styles.primaryColor,
                  width: 2,
                ),
              ),
              child: CircleAvatar(
                radius: 28,
                backgroundImage:
                    NetworkImage(ServerAssets().getAssetUrl(avatarUrl)),
              ),
            ),
            if (isOnline)
              Positioned(
                right: 2,
                bottom: 2,
                child: Container(
                  width: 14,
                  height: 14,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 2.5,
                    ),
                  ),
                ),
              ),
          ],
        ),
        title: Padding(
          padding: const EdgeInsets.only(bottom: 4.0),
          child: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black,
              fontSize: 16,
            ),
          ),
        ),
        subtitle: Text(
          lastMessage,
          style: const TextStyle(
            color: Colors.grey,
          ),
        ),
        trailing: Text(
          timestamp,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
      ),
    );
  }
}

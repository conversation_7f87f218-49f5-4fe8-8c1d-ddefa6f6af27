import 'package:flutter/material.dart';

class SkeletonLoaderCard extends StatelessWidget {
  const SkeletonLoaderCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      color: Colors.white,
      child: ListTile(
        leading: _buildSkeleton(40, 40),
        title: _buildSkeleton(120, 12),
        subtitle: _buildSkeleton(90, 10),
        trailing: _buildSkeleton(60, 18),
      ),
    );
  }

  Widget _buildSkeleton(double width, double height) {
    return AnimatedContainer(
      duration: const Duration(seconds: 1),
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey.shade300,
        borderRadius: BorderRadius.circular(8),
      ),
      margin: const EdgeInsets.all(4),
      child: AnimatedOpacity(
        opacity: 0.7,
        duration: const Duration(seconds: 1),
        child: Container(
          color: Colors.grey.shade200,
        ),
      ),
    );
  }
}

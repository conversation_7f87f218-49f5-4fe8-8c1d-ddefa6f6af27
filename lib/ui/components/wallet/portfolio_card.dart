import 'package:darve/ui/components/wallet/portfolio_card_floating_buttons.dart';
import 'package:flutter/material.dart';

class PortfolioCard extends StatefulWidget {
  final String balance;
  final String percentageIncrease;

  const PortfolioCard(
      {super.key, required this.balance, required this.percentageIncrease});

  @override
  State<PortfolioCard> createState() => _PortfolioCardState();
}

class _PortfolioCardState extends State<PortfolioCard> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 178,
      width: double.infinity,
      child: Stack(
        children: [
          SizedBox(
            height: 158,
            width: double.infinity,
            child: Card(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24)),
              elevation: 2,
              color: Colors.white,
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: <PERSON>umn(
                  children: [
                    const Text("Available Balance",
                        style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: Colors.black)),
                    const SizedBox(height: 10),
                    Text(widget.balance,
                        style: const TextStyle(
                            fontSize: 32, fontWeight: FontWeight.w500)),
                    Text("+${widget.percentageIncrease}%",
                        style: const TextStyle(
                            color: Color(0xFF53E16A),
                            fontSize: 14,
                            fontWeight: FontWeight.w400)),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),
          const Positioned(
            bottom: 0,
            left: 16,
            right: 16,
            child: PortfolioCardFloatingButtons(),
          ),
        ],
      ),
    );
  }
}

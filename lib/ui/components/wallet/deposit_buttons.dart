import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class AmountSelector extends StatefulWidget {
  final int? selectedAmount;
  final Function(int?) onAmountSelected;

  const AmountSelector(
      {super.key,
      required this.selectedAmount,
      required this.onAmountSelected});

  @override
  State<AmountSelector> createState() => _AmountSelectorState();
}

class _AmountSelectorState extends State<AmountSelector> {
  String customText = "Custom";
  TextEditingController customController = TextEditingController();

  void _selectAmount(int amount) {
    setState(() {
      widget.onAmountSelected(amount);
      customText = "Custom";
    });
  }

  void _showCustomInputDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          title: const Text("Enter Custom Amount"),
          content: TextField(
            controller: customController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(hintText: "Enter amount"),
            onChanged: (value) {
              if (value.length > 10) {
                customController.text = value.substring(0, 10);
                customController.selection = TextSelection.fromPosition(
                  TextPosition(offset: customController.text.length),
                );
              }
              customController.text =
                  customController.text.replaceAll(RegExp(r'[^0-9]'), '');
            },
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text("Cancel"),
            ),
            TextButton(
              onPressed: () {
                if (customController.text.isNotEmpty) {
                  setState(() {
                    customText = "\$${customController.text}";
                    widget
                        .onAmountSelected(int.tryParse(customController.text));
                  });
                }
                Navigator.pop(context);
              },
              child: const Text("OK"),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildButton(100),
          _buildButton(200),
          _buildButton(500),
          _buildCustomButton(),
        ],
      ),
    );
  }

  Widget _buildButton(int amount) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: ElevatedButton(
        onPressed: () => _selectAmount(amount),
        style: ElevatedButton.styleFrom(
          backgroundColor: widget.selectedAmount == amount
              ? Styles.primaryColor
              : Colors.white,
          side: const BorderSide(color: Styles.primaryColor, width: 2),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
        child: Text(
          "\$${amount.toString()}",
          style: TextStyle(
              color: widget.selectedAmount == amount
                  ? Colors.white
                  : Styles.primaryColor),
        ),
      ),
    );
  }

  Widget _buildCustomButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5),
      child: ElevatedButton(
        onPressed: _showCustomInputDialog,
        style: ElevatedButton.styleFrom(
          backgroundColor:
              widget.selectedAmount != null && customText != "Custom"
                  ? Styles.primaryColor
                  : Colors.white,
          side: const BorderSide(color: Styles.primaryColor, width: 2),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
        child: Text(
          customText.length > 10
              ? "${customText.substring(0, 10)}..."
              : customText,
          style: TextStyle(
            color: widget.selectedAmount != null && customText != "Custom"
                ? Colors.white
                : Styles.primaryColor,
          ),
        ),
      ),
    );
  }
}

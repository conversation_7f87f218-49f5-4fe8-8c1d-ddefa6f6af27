import 'package:darve/ui/wallet/deposit_page.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class PortfolioCardFloatingButtons extends StatelessWidget {
  const PortfolioCardFloatingButtons({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        OutlinedButton.icon(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const DepositPage(),
              ),
            );
          },
          icon: Image.asset(
            'assets/wallet/wallet.png',
            width: 18,
            height: 18,
            color: Colors.white,
          ),
          label: const Text("Deposit",
              style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                  color: Colors.white)),
          style: ElevatedButton.styleFrom(
            backgroundColor: Styles.primaryColor,
            minimumSize: const Size(145, 40),
          ),
        ),
        OutlinedButton.icon(
          onPressed: () {},
          icon: Image.asset(
            'assets/wallet/arrow_down.png',
            width: 18,
            height: 18,
            color: Colors.white,
          ),
          label: const Text("Withdraw",
              style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                  color: Colors.white)),
          style: ElevatedButton.styleFrom(
            backgroundColor: Styles.primaryColor,
            minimumSize: const Size(145, 40),
          ),
        ),
      ],
    );
  }
}

import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';

class YourBalance extends StatefulWidget {
  final int yourBalance;
  const YourBalance({required this.yourBalance, super.key});

  @override
  State<YourBalance> createState() => _YourBalanceState();
}

class _YourBalanceState extends State<YourBalance> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            "YOUR BALANCE",
            style: Styles.smallThinLightTextStyle,
          ),
          Text(
            "\$${widget.yourBalance}",
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}

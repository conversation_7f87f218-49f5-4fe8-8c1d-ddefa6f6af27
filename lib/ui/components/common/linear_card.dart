import 'package:darve/utils/server_assets.dart';
import 'package:flutter/material.dart';

class LinearCard extends StatelessWidget {
  final String userName;
  final String? imageUrl;

  const LinearCard({
    super.key,
    required this.userName,
    this.imageUrl,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Avatar
          CircleAvatar(
            radius: 24,
            backgroundImage: imageUrl != null
                ? NetworkImage(ServerAssets().getAssetUrl(imageUrl!))
                : null,
            child: imageUrl == null ? Text(userName[0].toUpperCase()) : null,
          ),

          // Username
          const SizedBox(width: 16),
          Text(
            userName,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 15,
            ),
          ),

          const Spacer(),

          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: () {},
                icon: const Icon(Icons.favorite_border, size: 22),
                padding: const EdgeInsets.all(2),
                constraints: const BoxConstraints(),
              ),
              IconButton(
                onPressed: () {},
                icon: const Icon(Icons.chat_bubble_outline, size: 22),
                padding: const EdgeInsets.all(2),
                constraints: const BoxConstraints(),
              ),
              IconButton(
                onPressed: () {},
                icon: const Icon(Icons.send_outlined, size: 22),
                padding: const EdgeInsets.all(2),
                constraints: const BoxConstraints(),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

import 'dart:io';
import 'package:darve/utils/errors.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';

class PictureUploader extends StatefulWidget {
  final Function voidCallback;
  const PictureUploader({super.key, required this.voidCallback});
  @override
  State<PictureUploader> createState() => _PictureUploaderState();
}

class _PictureUploaderState extends State<PictureUploader> {
  File? selectedImage;

  void pickFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.image,
    );

    if (result != null) {
      widget.voidCallback(result.files.single.path!);
      File file = File(result.files.single.path!);

      setState(() {
        selectedImage = file;
      });
    } else {
      ErrorsHandle().displayErrorToast("No file picked!");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: pickFile,
        child: Container(
          width: 225,
          height: 280,
          decoration: BoxDecoration(
            // shape: BoxShape.circle,
            borderRadius: BorderRadius.circular(4.0),
            color: Colors.grey[100],
            image: selectedImage != null
                ? DecorationImage(
                    image: FileImage(selectedImage!),
                    fit: BoxFit.cover,
                  )
                : null,
          ),
          child: selectedImage == null
              ? Icon(
                  Icons.camera_alt,
                  color: Colors.grey.shade700,
                  size: 40,
                )
              : null,
        ),
      ),
    );
  }
}

import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';

/// Reusable PIN Input Component for Verification Codes
/// Used across email verification, mobile verification, delete account verification, etc.
/// Provides consistent styling and behavior with customizable themes
class VerificationPinInput extends StatelessWidget {
  final Function(String) onChanged;
  final Function(String)? onCompleted;
  final int length;
  final PinInputTheme theme;
  final bool hasError;
  final String? errorText;

  const VerificationPinInput({
    super.key,
    required this.onChanged,
    this.onCompleted,
    this.length = 6,
    this.theme = PinInputTheme.primary,
    this.hasError = false,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Pinput(
          length: length,
          onChanged: onChanged,
          onCompleted: onCompleted,
          defaultPinTheme: _getDefaultTheme(),
          focusedPinTheme: _getFocusedTheme(),
          submittedPinTheme: _getSubmittedTheme(),
          errorPinTheme: _getErrorTheme(),
          keyboardType: TextInputType.number,
          forceErrorState: hasError,
        ),
        if (hasError && errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            errorText!,
            style: const TextStyle(
              color: Colors.red,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  PinTheme _getDefaultTheme() {
    switch (theme) {
      case PinInputTheme.primary:
        return PinTheme(
          width: 48,
          height: 56,
          textStyle: const TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Styles.primaryColor.withAlpha((255 / 10).toInt()),
            border: Border.all(
              color: Colors.transparent,
              width: 1,
            ),
          ),
        );
      case PinInputTheme.grey:
        return PinTheme(
          width: 56,
          height: 64,
          textStyle: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: const Color(0xFFF5F5F5),
            border: Border.all(
              color: Colors.transparent,
              width: 1,
            ),
          ),
        );
      case PinInputTheme.outlined:
        return PinTheme(
          width: 48,
          height: 56,
          textStyle: const TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
            border: Border.all(
              color: Colors.grey[300]!,
              width: 1,
            ),
          ),
        );
    }
  }

  PinTheme _getFocusedTheme() {
    switch (theme) {
      case PinInputTheme.primary:
        return _getDefaultTheme().copyWith(
          decoration: _getDefaultTheme().decoration!.copyWith(
            border: Border.all(
              color: Styles.primaryColor,
              width: 2,
            ),
          ),
        );
      case PinInputTheme.grey:
        return _getDefaultTheme().copyWith(
          decoration: _getDefaultTheme().decoration!.copyWith(
            border: Border.all(
              color: Styles.primaryColor,
              width: 2,
            ),
          ),
        );
      case PinInputTheme.outlined:
        return _getDefaultTheme().copyWith(
          decoration: _getDefaultTheme().decoration!.copyWith(
            border: Border.all(
              color: Styles.primaryColor,
              width: 2,
            ),
          ),
        );
    }
  }

  PinTheme _getSubmittedTheme() {
    switch (theme) {
      case PinInputTheme.primary:
        return _getDefaultTheme().copyWith(
          decoration: _getDefaultTheme().decoration!.copyWith(
            border: Border.all(
              color: Colors.grey[300]!,
              width: 1,
            ),
          ),
        );
      case PinInputTheme.grey:
        return _getDefaultTheme().copyWith(
          decoration: _getDefaultTheme().decoration!.copyWith(
            border: Border.all(
              color: Colors.grey[300]!,
              width: 1,
            ),
          ),
        );
      case PinInputTheme.outlined:
        return _getDefaultTheme().copyWith(
          decoration: _getDefaultTheme().decoration!.copyWith(
            border: Border.all(
              color: Colors.grey[400]!,
              width: 1,
            ),
          ),
        );
    }
  }

  PinTheme _getErrorTheme() {
    return _getDefaultTheme().copyWith(
      textStyle: _getDefaultTheme().textStyle!.copyWith(
        color: Colors.red,
      ),
      decoration: _getDefaultTheme().decoration!.copyWith(
        border: Border.all(
          color: Colors.red,
          width: 2,
        ),
      ),
    );
  }
}

/// Theme options for PIN input styling
enum PinInputTheme {
  /// Primary blue theme - used for email/mobile verification
  primary,
  
  /// Grey theme - used for delete account verification (matches design image)
  grey,
  
  /// Outlined theme - alternative clean design
  outlined,
}

/// Static factory methods for common use cases
extension VerificationPinInputFactory on VerificationPinInput {
  /// Create PIN input for email verification
  static Widget forEmailVerification({
    required Function(String) onChanged,
    Function(String)? onCompleted,
    bool hasError = false,
    String? errorText,
  }) {
    return VerificationPinInput(
      onChanged: onChanged,
      onCompleted: onCompleted,
      theme: PinInputTheme.primary,
      hasError: hasError,
      errorText: errorText,
    );
  }

  /// Create PIN input for mobile verification
  static Widget forMobileVerification({
    required Function(String) onChanged,
    Function(String)? onCompleted,
    bool hasError = false,
    String? errorText,
  }) {
    return VerificationPinInput(
      onChanged: onChanged,
      onCompleted: onCompleted,
      theme: PinInputTheme.primary,
      hasError: hasError,
      errorText: errorText,
    );
  }

  /// Create PIN input for delete account verification (matches design image)
  static Widget forDeleteAccountVerification({
    required Function(String) onChanged,
    Function(String)? onCompleted,
    bool hasError = false,
    String? errorText,
  }) {
    return VerificationPinInput(
      onChanged: onChanged,
      onCompleted: onCompleted,
      theme: PinInputTheme.grey,
      hasError: hasError,
      errorText: errorText,
    );
  }

  /// Create PIN input with custom theme
  static Widget withTheme({
    required Function(String) onChanged,
    Function(String)? onCompleted,
    required PinInputTheme theme,
    bool hasError = false,
    String? errorText,
    int length = 6,
  }) {
    return VerificationPinInput(
      onChanged: onChanged,
      onCompleted: onCompleted,
      theme: theme,
      hasError: hasError,
      errorText: errorText,
      length: length,
    );
  }
}

import 'dart:io';
import 'package:darve/utils/errors.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';

class ProfilePictureUploader extends StatefulWidget {
  final Function voidCallback;
  final bool isLarge;

  const ProfilePictureUploader({
    super.key,
    required this.voidCallback,
    this.isLarge = false,
  });

  @override
  State<ProfilePictureUploader> createState() => _ProfilePictureUploaderState();
}

class _ProfilePictureUploaderState extends State<ProfilePictureUploader> {
  File? selectedImage;

  void pickFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.image,
    );

    if (result != null) {
      widget.voidCallback(result.files.single.path!);
      File file = File(result.files.single.path!);

      setState(() {
        selectedImage = file;
      });
    } else {
      ErrorsHandle().displayErrorToast("No file picked!");
    }
  }

  @override
  Widget build(BuildContext context) {
    double size = widget.isLarge ? 174 : 150;

    return Center(
      child: GestureDetector(
        onTap: pickFile,
        child: Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.grey.shade300,
            image: selectedImage != null
                ? DecorationImage(
                    image: FileImage(selectedImage!),
                    fit: BoxFit.cover,
                  )
                : null,
          ),
          child: selectedImage == null
              ? Icon(
                  Icons.camera_alt,
                  color: Colors.grey.shade700,
                  size: size * 0.4,
                )
              : null,
        ),
      ),
    );
  }
}

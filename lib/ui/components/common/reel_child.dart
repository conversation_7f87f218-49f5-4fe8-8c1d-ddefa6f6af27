import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:darve/api/models/post_model.dart';
import 'package:darve/utils/medialinks.dart';

class ReelChild extends StatefulWidget {
  final PostModel post;
  const ReelChild(this.post, {super.key});

  @override
  State<ReelChild> createState() => _ReelChildState();
}

class _ReelChildState extends State<ReelChild> {
  late VideoPlayerController _controller;
  bool isVideo = false;

  @override
  void initState() {
    super.initState();
    final mediaUrl = MediaLinksHelper().getPostReel(widget.post);
    if (mediaUrl?.endsWith('.mp4')==true) {
      isVideo = true;
      _controller = VideoPlayerController.networkUrl(Uri.parse(mediaUrl!))
        ..initialize().then((_) {
          setState(() {});
          _controller.setLooping(true);
          _controller.play();
        });
    }
  }

  @override
  void dispose() {
    if (isVideo) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: isVideo
          ? _controller.value.isInitialized
              ? VideoPlayer(_controller)
              : const Center(child: CircularProgressIndicator())
          : Image.network(MediaLinksHelper().getPostReel(widget.post)??"",
              fit: BoxFit.cover, errorBuilder: (context, error, stackTrace) {
              return const Center(child: Text('Error loading media'));
            }),
    );
  }
}

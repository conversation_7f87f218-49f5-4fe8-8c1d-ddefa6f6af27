import 'package:darve/routes/route_helper.dart';
import 'package:darve/api/models/post_model.dart';
import 'package:flutter/material.dart';

class UserAggregate extends StatefulWidget {
  final String followingNr;
  final String followersNr;
  final List<PostModel> posts;
  final String userId;
  const UserAggregate(
      this.followingNr, this.followersNr, this.posts, this.userId,
      {super.key});

  @override
  State<UserAggregate> createState() => _UserAggregateState();
}

class _UserAggregateState extends State<UserAggregate> {
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        InkWell(
          onTap: () {
            RouteHelper.goToProfileInsights(
              userId: widget.userId,
              initialTab: 0, // Followers tab
            );
          },
          child: Column(
            children: [
              Text(
                widget.followersNr.toString(),
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              const Text(
                "Followers",
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
        Container(
          height: 40,
          width: 1,
          color: Colors.grey[300],
        ),
        Column(
          children: [
            Text(
              "${widget.posts.length}",
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 2),
            const Text(
              "Posts",
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        Container(
          height: 40,
          width: 1,
          color: Colors.grey[300],
        ),
        InkWell(
          onTap: () {
            RouteHelper.goToProfileInsights(
              userId: widget.userId,
              initialTab: 1, // Following tab
            );
          },
          child: Column(
            children: [
              Text(
                widget.followingNr,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              const Text(
                "Following",
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

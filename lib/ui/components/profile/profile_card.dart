import 'package:darve/utils/server_assets.dart';
import 'package:flutter/material.dart';

class ProfileCard extends StatelessWidget {
  final String userName;
  final String subText;
  final String? imageUrl;
  final String btnVal;
  final VoidCallback onBtnClick;

  const ProfileCard({
    super.key,
    required this.userName,
    required this.subText,
    required this.btnVal,
    required this.onBtnClick,
    this.imageUrl,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      color: Colors.white,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.43,
        padding: const EdgeInsets.fromLTRB(8, 8, 8, 4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: .1),
                    spreadRadius: 2,
                    blurRadius: 7,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: CircleAvatar(
                radius: 35,
                backgroundImage: imageUrl != null && imageUrl!.isNotEmpty
                    ? NetworkImage(ServerAssets().getAssetUrl(imageUrl!))
                    : null,
                child: imageUrl == null
                    ? Text(userName[0].toUpperCase(),
                        style: const TextStyle(fontSize: 25))
                    : null,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              userName,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 15,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: btnVal == "Following"
                  ? ElevatedButton(
                      onPressed: onBtnClick,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.black,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: EdgeInsets.zero,
                      ),
                      child: Text(
                        btnVal,
                        style: const TextStyle(fontSize: 13),
                      ),
                    )
                  : OutlinedButton(
                      onPressed: () {},
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.black,
                        side: const BorderSide(color: Colors.black),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: EdgeInsets.zero,
                      ),
                      child: Text(
                        btnVal,
                        style: const TextStyle(fontSize: 13),
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

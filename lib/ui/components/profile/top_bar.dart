import 'package:flutter/material.dart';
import 'package:darve/routes/route_helper.dart';

class TopBar extends StatefulWidget {
  final String title;
  final bool isTransparent;
  final bool isCurrentUser;
  final VoidCallback? callback;
  const TopBar(
      {super.key,
      required this.title,
      this.isTransparent = false,
      this.isCurrentUser = false,
      this.callback});

  @override
  State<TopBar> createState() => _TopBarState();
}

class _TopBarState extends State<TopBar> {
  Color getColor() {
    return widget.isTransparent ? Colors.white : Colors.black;
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (!widget.isCurrentUser)
            IconButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                icon: Icon(
                  Icons.arrow_back_ios_new,
                  color: getColor(),
                )),
          if (widget.isCurrentUser) const SizedBox(width: 70.0),
          Text(
            widget.title,
            style: TextStyle(
                fontSize: 18, fontWeight: FontWeight.bold, color: getColor()),
          ),
          if (widget.isCurrentUser)
            GestureDetector(
              onTap: () {
                RouteHelper.goToSettings();
              },
              child: Container(
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(100)),
                margin: const EdgeInsets.only(right: 10.0),
                child: const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Icon(Icons.settings_outlined),
                ),
              ),
            ),
          if (!widget.isCurrentUser) const Spacer(),
        ],
      ),
    );
  }
}

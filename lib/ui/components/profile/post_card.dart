import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:darve/ui/components/profile/personal_reel_view.dart';
import 'package:darve/api/models/post_model.dart';
import 'package:darve/utils/server_assets.dart';

class PostCard extends StatefulWidget {
  final String? imageUrl;
  final PostModel post;

  const PostCard({
    super.key,
    required this.post,
    required this.imageUrl,
  });

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> {
  late VideoPlayerController _controller;
  bool isVideo = false;

  @override
  void initState() {
    super.initState();
    if (widget.imageUrl?.endsWith('.mp4')==true) {
      isVideo = true;
      _controller = VideoPlayerController.networkUrl(
          Uri.parse(ServerAssets().getAssetUrl(widget.imageUrl!)))
        ..initialize().then((_) {
          setState(() {});
        });
    }
  }

  @override
  void dispose() {
    if (isVideo) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PersonalReelView(widget.post),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
        ),
        child: isVideo
            ? _controller.value.isInitialized
                ? ClipRRect(
                    borderRadius:
                        BorderRadius.circular(10), // Add border radius here
                    child: Stack(
                      children: [
                        VideoPlayer(_controller),
                        const Align(
                          alignment: Alignment.center,
                          child: Icon(
                            Icons.play_circle_fill,
                            color: Colors.white,
                            size: 50,
                          ),
                        ),
                      ],
                    ),
                  )
                : const Center(child: CircularProgressIndicator())
            : ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: Container(
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: widget.imageUrl!=null?NetworkImage(
                          ServerAssets().getAssetUrl(widget.imageUrl!)):const AssetImage('assets/images/darve.png'),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
      ),
    );
  }
}

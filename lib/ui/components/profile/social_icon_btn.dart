import 'package:darve/utils/errors.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

class SocialIconBtn extends StatelessWidget {
  final IconData icon;
  final String? url;
  const SocialIconBtn(this.icon, this.url, {super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        shape: BoxShape.circle,
      ),
      child: Icon<PERSON><PERSON>on(
        icon: FaIcon(
          icon,
          color: Colors.black,
        ),
        onPressed: () async {
          if (url != null) {
            if (await canLaunchUrl(Uri.parse(url!))) {
              await launchUrl(Uri.parse(url!),
                  mode: LaunchMode.externalApplication);
            } else {
              ErrorsHandle()
                  .displayErrorToast("Can not launch url", "urlLaunchError");
            }
          }
        },
      ),
    );
  }
}

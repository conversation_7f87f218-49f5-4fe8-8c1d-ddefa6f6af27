import 'package:darve/ui/components/profile/social_icon_btn.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class SocialGroup extends StatelessWidget {
  final List<String> socialLinks;
  final List<String> platforms;
  const SocialGroup(this.socialLinks, this.platforms, {super.key});

  @override
  Widget build(BuildContext context) {
    return const Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SocialIconBtn(FontAwesomeIcons.facebook, "sdfsdf"),
        <PERSON><PERSON><PERSON><PERSON>(width: 31),
        SocialIconBtn(FontAwesomeIcons.twitter, "sdfsdf"),
        <PERSON><PERSON><PERSON><PERSON>(width: 31),
        SocialIconBtn(FontAwesomeIcons.instagram, "sdfsdf"),
        <PERSON><PERSON><PERSON><PERSON>(width: 31),
        SocialIconBtn(FontAwesomeIcons.youtube, "sdfsdf"),
      ],
    );
  }
}

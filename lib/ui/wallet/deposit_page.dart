import 'package:darve/ui/components/wallet/deposit_buttons.dart';
import 'package:darve/ui/components/wallet/your_balance.dart';
import 'package:darve/utils/show_snackbar.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';

class DepositPage extends StatefulWidget {
  const DepositPage({super.key});

  @override
  State<DepositPage> createState() => _DepositPageState();
}

class _DepositPageState extends State<DepositPage> {
  int? selectedAmount;
  int yourBalance = 0;
  bool isTxInProgress = false;

  @override
  void initState() {
    ServiceProvider.walletRepository.getBalance().then((val) {
      if (mounted) {
        setState(() {
          yourBalance = val['balance']['balance_usd'];
        });
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Deposit',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w800,
            fontSize: 20,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          children: [
            const SizedBox(height: 4.0),
            YourBalance(
              yourBalance: yourBalance,
            ),
            const SizedBox(height: 24.0),
            const Text(
              "Choose your amount",
              style: Styles.thinLightTextStyle,
            ),
            const SizedBox(height: 8.0),
            AmountSelector(
              selectedAmount: selectedAmount,
              onAmountSelected: (amt) {
                setState(() {
                  selectedAmount = amt;
                });
              },
            ),
            const SizedBox(height: 8.0),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(right: 12.0),
                    child: isTxInProgress
                        ? const CircularProgressIndicator()
                        : ElevatedButton.icon(
                            onPressed: () async {
                              try {
                                setState(() {
                                  isTxInProgress = true;
                                });
                                final clientSecret = await ServiceProvider.walletRepository.createPaymentIntent(
                                    "${selectedAmount ?? 0}");

                                await Stripe.instance.initPaymentSheet(
                                  paymentSheetParameters:
                                      SetupPaymentSheetParameters(
                                    paymentIntentClientSecret: clientSecret,
                                    style: ThemeMode.light,
                                    customFlow: false,
                                    merchantDisplayName: "Darve",
                                  ),
                                );

                                await Stripe.instance.presentPaymentSheet();

                                SnackbarHelper.showChallengeSnackbar(
                                    context: context,
                                    content: "Payment Successful",
                                    bgColor: Colors.green,
                                    duration: 2000);
                              } catch (e) {
                                ErrorsHandle().displayErrorToast(e.toString());
                              }
                              if (mounted) {
                                setState(() {
                                  isTxInProgress = false;
                                });
                              }
                              Navigator.pop(context);
                            },
                            icon:
                                const Icon(Icons.payment, color: Colors.white),
                            label: const Text(
                              "Deposit",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Styles.primaryColor,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                            ),
                          ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16.0),
          ],
        ),
      ),
    );
  }
}

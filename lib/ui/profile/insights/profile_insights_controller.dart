import 'package:darve/api/models/follower_model.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/ui/core/entity_state_widget.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/result.dart';
import 'package:darve/utils/show_snackbar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileInsightsController extends GetxController {
  final String userId;
  final int initialTab;

  final Rx<ViewModel<List<FollowerModel>>> followersState =
      const ViewModel<List<FollowerModel>>.loading().obs;

  final Rx<ViewModel<List<FollowerModel>>> followingState =
      const ViewModel<List<FollowerModel>>.loading().obs;

  final ErrorsHandle _errorHandler = ErrorsHandle();

  ProfileInsightsController({
    required this.userId,
    this.initialTab = 0,
  });

  @override
  void onInit() {
    super.onInit();
    // Auto-load data when controller is initialized
    loadInsightsData();
  }

  /// Load both followers and following data
  Future<void> loadInsightsData() async {
    // Load both followers and following concurrently
    await Future.wait([
      loadFollowers(),
      loadFollowing(),
    ]);
  }

  /// Load followers list
  Future<void> loadFollowers() async {
    followersState.value = const ViewModel<List<FollowerModel>>.loading();

    final result =
        await ServiceProvider.profileRepositoryNew.getFollowers(userId);
    followersState.value = result.toListViewModel<FollowerModel>(
      onError: (error) =>
          _errorHandler.displayErrorToast(error, 'loadFollowers'),
    );
  }

  /// Load following list
  Future<void> loadFollowing() async {
    followingState.value = const ViewModel<List<FollowerModel>>.loading();
    final result =
        await ServiceProvider.profileRepositoryNew.getFollowing(userId);
    followingState.value = result.toListViewModel<FollowerModel>(
      onError: (error) =>
          _errorHandler.displayErrorToast(error, 'loadFollowing'),
    );
  }

  /// Refresh all data (public method for UI)
  Future<void> refreshInsightsData() async {
    await loadInsightsData();
  }

  /// Unfollow a user from the following list
  Future<void> unfollowUser(
      FollowerModel userToUnfollow, BuildContext context) async {
    // Get user details first
    final unfollowResult = await ServiceProvider.profileRepositoryNew
        .unfollowUser(userToUnfollow.id);
    unfollowResult.toViewModel(
        onError: (error) =>
            _errorHandler.displayErrorToast(error, 'unfollowUser'),
        onSuccess: (_) {
          // Refresh the data to update the lists
          loadFollowing();

          // Show success feedback - check if context is still mounted
          if (context.mounted) {
            SnackbarHelper.showFollowSnackbar(
              context: context,
              imageUri: userToUnfollow.imageUrl,
              username: userToUnfollow.username,
              bgColor: Colors.red,
              isFollowed: false,
            );
          }
        });
  }

  /// Get followers data for direct access (if needed)
  List<FollowerModel>? get followers => followersState.value.data;

  /// Get following data for direct access (if needed)
  List<FollowerModel>? get following => followingState.value.data;
}

import 'package:darve/ui/profile/insights/profile_insights_controller.dart';
import 'package:get/get.dart';

class ProfileInsightsBinding extends Bindings {
  @override
  void dependencies() {
    // Get route arguments
    final args = Get.arguments as Map<String, dynamic>;
    final userId = args['userId'] as String;
    final initialTab = args['initialTab'] as int? ?? 0;

    // Register ProfileInsightsController with proper parameters
    Get.lazyPut<ProfileInsightsController>(
      () => ProfileInsightsController(
        userId: userId,
        initialTab: initialTab,
      ),
      tag: 'profile_insights_$userId', // Use unique tag for multiple instances
    );
  }
}

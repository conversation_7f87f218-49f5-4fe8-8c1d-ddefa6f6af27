import 'dart:convert';

class ProfileEditRequestModel {
  final String? fullName;
  final String? username;
  final String? bio;
  final String? email;
  final String? mobileNumber;
  final String? dateOfBirth;
  final List<String>? socialLinks;
  final String? profileImagePath;

  const ProfileEditRequestModel({
    this.fullName,
    this.username,
    this.bio,
    this.email,
    this.mobileNumber,
    this.dateOfBirth,
    this.socialLinks,
    this.profileImagePath,
  });

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    
    if (fullName != null) data['full_name'] = fullName;
    if (username != null) data['username'] = username;
    if (bio != null) data['bio'] = bio;
    if (email != null) data['email'] = email;
    if (mobileNumber != null) data['mobile_number'] = mobileNumber;
    if (dateOfBirth != null) data['date_of_birth'] = dateOfBirth;
    if (socialLinks != null) data['social_links'] = socialLinks;
    
    return data;
  }

  /// Convert to JSON string for API request
  String toJsonString() {
    return jsonEncode(toJson());
  }

  /// Create copy with updated fields
  ProfileEditRequestModel copyWith({
    String? fullName,
    String? username,
    String? bio,
    String? email,
    String? mobileNumber,
    String? dateOfBirth,
    List<String>? socialLinks,
    String? profileImagePath,
  }) {
    return ProfileEditRequestModel(
      fullName: fullName ?? this.fullName,
      username: username ?? this.username,
      bio: bio ?? this.bio,
      email: email ?? this.email,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      socialLinks: socialLinks ?? this.socialLinks,
      profileImagePath: profileImagePath ?? this.profileImagePath,
    );
  }

  /// Check if any field has been modified
  bool get hasChanges {
    return fullName != null ||
           username != null ||
           bio != null ||
           email != null ||
           mobileNumber != null ||
           dateOfBirth != null ||
           socialLinks != null ||
           profileImagePath != null;
  }

  /// Get list of modified fields for logging/debugging
  List<String> get modifiedFields {
    final List<String> fields = [];
    
    if (fullName != null) fields.add('fullName');
    if (username != null) fields.add('username');
    if (bio != null) fields.add('bio');
    if (email != null) fields.add('email');
    if (mobileNumber != null) fields.add('mobileNumber');
    if (dateOfBirth != null) fields.add('dateOfBirth');
    if (socialLinks != null) fields.add('socialLinks');
    if (profileImagePath != null) fields.add('profileImagePath');
    
    return fields;
  }
}

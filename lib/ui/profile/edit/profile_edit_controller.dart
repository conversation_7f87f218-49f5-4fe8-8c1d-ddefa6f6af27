import 'package:darve/api/models/profile_data_model.dart';
import 'package:darve/api/repositories/profile_repository.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/services/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/ui/core/entity_state_widget.dart';
import 'package:darve/ui/profile/edit/models/profile_edit_form_model.dart';
import 'package:darve/ui/profile/edit/models/profile_edit_request_model.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/result.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';

class ProfileEditController extends GetxController {
  // Form controllers
  final TextEditingController fullNameController = TextEditingController();
  final TextEditingController usernameController = TextEditingController();
  final TextEditingController bioController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController mobileController = TextEditingController();
  final TextEditingController dateOfBirthController = TextEditingController();

  // Form key for validation
  final formKey = GlobalKey<FormState>();

  // State management using ViewModel pattern
  final Rx<ViewModel<ProfileEditFormModel>> formState = const ViewModel<ProfileEditFormModel>.loading().obs;
  final Rx<ViewModel<bool>> saveState = const ViewModel<bool>.content(false).obs;

  // Form data
  late ProfileEditFormModel _originalForm;
  final Rx<ProfileEditFormModel> currentForm = ProfileEditFormModel.empty().obs;

  // Image handling
  final RxString selectedImagePath = ''.obs;
  final RxBool isImageLoading = false.obs;

  // Validation errors
  final RxMap<String, String> validationErrors = <String, String>{}.obs;

  // Dependencies
  late final AuthService authService;
  late final ProfileRepository _profileRepository;
  final ErrorsHandle _errorHandler = ErrorsHandle();

  // Profile data
  final String userId;
  final String username;

  ProfileEditController({required this.userId, required this.username});

  @override
  void onInit() {
    super.onInit();
    authService = AuthProvider.auth;
    _profileRepository = ServiceProvider.profileRepositoryNew;
    
    // Load initial profile data
    loadProfileData();
  }

  @override
  void onClose() {
    fullNameController.dispose();
    usernameController.dispose();
    bioController.dispose();
    emailController.dispose();
    mobileController.dispose();
    dateOfBirthController.dispose();
    super.onClose();
  }

  /// Load profile data for editing
  Future<void> loadProfileData() async {
    formState.value = const ViewModel<ProfileEditFormModel>.loading();

    final result = await _profileRepository.getProfileData(username);
    
    formState.value = result.toViewModel(
      onError: (error) => _errorHandler.displayErrorToast(error, 'loadProfileData'),
      onSuccess: (profileData) {
        final form = ProfileEditFormModel.fromProfileData(profileData);
        _originalForm = form;
        currentForm.value = form;
        _populateControllers(form);
        return form;
      },
    );
  }

  /// Populate form controllers with data
  void _populateControllers(ProfileEditFormModel form) {
    fullNameController.text = form.fullName;
    usernameController.text = form.username;
    bioController.text = form.bio;
    emailController.text = form.email;
    mobileController.text = form.mobileNumber;
    dateOfBirthController.text = form.dateOfBirth;
  }

  /// Update form field
  void updateField(String field, String value) {
    switch (field) {
      case 'fullName':
        currentForm.value = currentForm.value.copyWith(fullName: value);
        break;
      case 'username':
        currentForm.value = currentForm.value.copyWith(username: value);
        break;
      case 'bio':
        currentForm.value = currentForm.value.copyWith(bio: value);
        break;
      case 'email':
        currentForm.value = currentForm.value.copyWith(email: value);
        break;
      case 'mobileNumber':
        currentForm.value = currentForm.value.copyWith(mobileNumber: value);
        break;
      case 'dateOfBirth':
        currentForm.value = currentForm.value.copyWith(dateOfBirth: value);
        break;
    }
    
    // Clear validation error for this field
    validationErrors.remove(field);
  }

  /// Pick profile image
  Future<void> pickProfileImage() async {
    try {
      isImageLoading.value = true;
      
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        selectedImagePath.value = result.files.single.path!;
        currentForm.value = currentForm.value.copyWith(
          profileImagePath: result.files.single.path!,
        );
      }
    } catch (e) {
      _errorHandler.displayErrorToast(e, 'pickProfileImage');
    } finally {
      isImageLoading.value = false;
    }
  }

  /// Remove selected image
  void removeSelectedImage() {
    selectedImagePath.value = '';
    currentForm.value = currentForm.value.copyWith(profileImagePath: null);
  }

  /// Validate form
  bool validateForm() {
    final errors = currentForm.value.validate();
    validationErrors.value = errors;
    return errors.isEmpty;
  }

  /// Save profile changes
  Future<void> saveProfile() async {
    if (!validateForm()) {
      Get.snackbar(
        'Validation Error',
        'Please fix the errors in the form',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
      return;
    }

    if (!hasChanges) {
      Get.snackbar(
        'No Changes',
        'No changes were made to save',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
      return;
    }

    saveState.value = const ViewModel<bool>.loading();

    // Create request model with only changed fields
    final request = _createUpdateRequest();
    
    final result = await _profileRepository.editProfile(
      request.toJsonString(),
      request.profileImagePath ?? '',
    );

    saveState.value = result.toViewModel(
      onError: (error) => _errorHandler.displayErrorToast(error, 'saveProfile'),
      onSuccess: (_) {
        Get.snackbar(
          'Success',
          'Profile updated successfully',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );

        // Navigate back with success result
        Get.back(result: true);
        return true;
      },
    );
  }

  /// Create update request with only changed fields
  ProfileEditRequestModel _createUpdateRequest() {
    final current = currentForm.value;
    final original = _originalForm;

    return ProfileEditRequestModel(
      fullName: current.fullName != original.fullName ? current.fullName : null,
      username: current.username != original.username ? current.username : null,
      bio: current.bio != original.bio ? current.bio : null,
      email: current.email != original.email ? current.email : null,
      mobileNumber: current.mobileNumber != original.mobileNumber ? current.mobileNumber : null,
      dateOfBirth: current.dateOfBirth != original.dateOfBirth ? current.dateOfBirth : null,
      socialLinks: !_listEquals(current.socialLinks, original.socialLinks) ? current.socialLinks : null,
      profileImagePath: selectedImagePath.value.isNotEmpty ? selectedImagePath.value : null,
    );
  }

  /// Helper method to compare lists
  bool _listEquals(List<String> a, List<String> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  /// Navigate to email verification
  Future<void> editEmail() async {
    // TODO: Navigate to email verification page
    // This will be similar to the existing VerifyEmailController flow
    Get.snackbar(
      'Email Verification',
      'Email verification flow will be implemented',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
      snackPosition: SnackPosition.TOP,
    );
  }

  /// Navigate to mobile verification
  Future<void> editMobile() async {
    // Import the mobile verification page when it's created
    // For now, show a placeholder
    Get.snackbar(
      'Mobile Verification',
      'Mobile verification flow will be implemented',
      backgroundColor: Colors.blue,
      colorText: Colors.white,
      snackPosition: SnackPosition.TOP,
    );
  }

  /// Select date of birth
  Future<void> selectDateOfBirth() async {
    final DateTime? picked = await showDatePicker(
      context: Get.context!,
      initialDate: DateTime.now().subtract(const Duration(days: 365 * 18)),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      final formattedDate = '${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}';
      dateOfBirthController.text = formattedDate;
      updateField('dateOfBirth', formattedDate);
    }
  }

  /// Check if form has changes
  bool get hasChanges => currentForm.value.hasChangesFrom(_originalForm) || selectedImagePath.value.isNotEmpty;

  /// Get loading states for UI
  bool get isLoading => formState.value.state == ViewState.loading;
  bool get isSaving => saveState.value.state == ViewState.loading;
  
  /// Get form data for UI
  ProfileEditFormModel? get formData => formState.value.data;
  
  /// Get validation error for field
  String? getFieldError(String field) => validationErrors[field];
  
  /// Check if field has error
  bool hasFieldError(String field) => validationErrors.containsKey(field);
}

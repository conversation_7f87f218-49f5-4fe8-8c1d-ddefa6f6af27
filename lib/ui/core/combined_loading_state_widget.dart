import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:darve/ui/core/entity_state_widget.dart';

/// A simple wrapper widget that shows a loading overlay when any of the provided
/// ViewModels is in loading state. Once all ViewModels are done loading 
/// (regardless of success/error), it shows the child content.
class CombinedLoadingStateWidget extends StatelessWidget {
  /// List of ViewModels to monitor for loading state
  final List<Rx<ViewModel>> viewModels;
  
  /// The content to show when all ViewModels are done loading
  final Widget child;
  
  /// Custom loading widget (optional)
  final Widget? loadingWidget;
  
  /// Loading message to display (optional)
  final String? loadingMessage;

  const CombinedLoadingStateWidget({
    super.key,
    required this.viewModels,
    required this.child,
    this.loadingWidget,
    this.loadingMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // Check if any ViewModel is currently loading
      final isAnyLoading = viewModels.any(
        (viewModel) => viewModel.value.state == ViewState.loading,
      );

      if (isAnyLoading) {
        return _buildLoadingState();
      }

      // All ViewModels are done loading, show the content
      return child;
    });
  }

  Widget _buildLoadingState() {
    return loadingWidget ?? _buildDefaultLoadingWidget();
  }

  Widget _buildDefaultLoadingWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            if (loadingMessage != null) ...[
              const SizedBox(height: 16),
              Text(
                loadingMessage!,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

import 'package:darve/ui/core/entity_state_widget.dart';
import 'package:darve/ui/core/combined_loading_state_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import 'example_controller.dart';

/// Comprehensive example page demonstrating EntityStateWidget and CombinedLoadingStateWidget
///
/// This page showcases:
/// - All 4 states (loading, content, empty, error)
/// - Custom builders for different states
/// - CombinedLoadingStateWidget for multiple ViewModels
/// - Different data types (single object, lists, paginated data)
/// - Error handling with retry functionality
/// - Search functionality
/// - Network error simulation
class ExamplePage extends GetView<ExampleController> {
  const ExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('EntityStateWidget Examples'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        actions: [
          // Demo controls
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'toggle_error',
                child: Text('Toggle Network Error'),
              ),
              const PopupMenuItem(
                value: 'toggle_empty',
                child: Text('Toggle Empty Data'),
              ),
              const PopupMenuItem(
                value: 'reset',
                child: Text('Reset & Reload'),
              ),
            ],
          ),
        ],
      ),
      body: DefaultTabController(
        length: 4,
        child: Column(
          children: [
            // Tab bar
            Container(
              color: Colors.grey[100],
              child: const TabBar(
                labelColor: Colors.blue,
                unselectedLabelColor: Colors.grey,
                indicatorColor: Colors.blue,
                tabs: [
                  Tab(text: 'Basic', icon: Icon(Icons.widgets)),
                  Tab(text: 'Custom', icon: Icon(Icons.build)),
                  Tab(text: 'Combined', icon: Icon(Icons.layers)),
                  Tab(text: 'Search', icon: Icon(Icons.search)),
                ],
              ),
            ),

            // Tab content
            Expanded(
              child: TabBarView(
                children: [
                  _buildBasicExamplesTab(),
                  _buildCustomBuildersTab(),
                  _buildCombinedLoadingTab(),
                  _buildSearchTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'toggle_error':
        controller.toggleNetworkError();
        break;
      case 'toggle_empty':
        controller.toggleEmptyData();
        break;
      case 'reset':
        controller.resetAndReload();
        break;
    }
  }

  /// Tab 1: Basic EntityStateWidget examples
  Widget _buildBasicExamplesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Single Object - User Profile'),
          SizedBox(
            height: 200,
            child: Card(
              child: EntityStateWidget<UserProfile>(
                model: controller.userProfileState,
                onRetry: () => controller.loadUserProfile(),
                emptyMessage: "No user profile available",
                emptyIcon: const Icon(
                  Icons.person_outline,
                  size: 64,
                  color: Colors.grey,
                ),
                itemBuilder: (profile) => _buildUserProfileCard(profile),
              ),
            ),
          ),

          const SizedBox(height: 24),
          _buildSectionHeader('List Data - Articles'),
          SizedBox(
            height: 300,
            child: Card(
              child: EntityStateWidget<List<Article>>(
                model: controller.articlesState,
                onRetry: () => controller.loadArticles(),
                emptyMessage: "No articles published yet",
                emptyIcon: const Icon(
                  Icons.article_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                itemBuilder: (articles) => _buildArticlesList(articles),
              ),
            ),
          ),

          const SizedBox(height: 24),
          _buildSectionHeader('Paginated Data - Comments'),
          SizedBox(
            height: 250,
            child: Card(
              child: EntityStateWidget<List<Comment>>(
                model: controller.commentsState,
                onRetry: () => controller.loadComments(isRefresh: true),
                emptyMessage: "No comments yet. Be the first to comment!",
                emptyIcon: const Icon(
                  Icons.comment_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                itemBuilder: (comments) => _buildCommentsList(comments),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Tab 2: Custom builders examples
  Widget _buildCustomBuildersTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Custom Loading Widget'),
          SizedBox(
            height: 200,
            child: Card(
              child: EntityStateWidget<UserProfile>(
                model: controller.userProfileState,
                onRetry: () => controller.loadUserProfile(),
                // Custom loading builder
                loadingBuilder: () => Container(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          const CircularProgressIndicator(
                            strokeWidth: 3,
                            color: Colors.blue,
                          ),
                          Icon(
                            Icons.person,
                            color: Colors.blue[300],
                            size: 24,
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Loading user profile...',
                        style: TextStyle(
                          color: Colors.blue[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Please wait while we fetch your data',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                itemBuilder: (profile) => _buildUserProfileCard(profile),
              ),
            ),
          ),

          const SizedBox(height: 24),
          _buildSectionHeader('Custom Error Widget'),
          SizedBox(
            height: 250,
            child: Card(
              child: EntityStateWidget<List<Article>>(
                model: controller.articlesState,
                onRetry: () => controller.loadArticles(),
                // Custom error builder
                errorBuilder: (error, onRetry) => Container(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.red[50],
                          borderRadius: BorderRadius.circular(50),
                        ),
                        child: Icon(
                          Icons.cloud_off,
                          size: 48,
                          color: Colors.red[400],
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Oops! Something went wrong',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.red[700],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        error?.toString() ?? 'Unknown error occurred',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          OutlinedButton.icon(
                            onPressed: () {
                              // Could show more details
                            },
                            icon: const Icon(Icons.info_outline),
                            label: const Text('Details'),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(width: 16),
                          ElevatedButton.icon(
                            onPressed: onRetry,
                            icon: const Icon(Icons.refresh),
                            label: const Text('Try Again'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red[600],
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                itemBuilder: (articles) => _buildArticlesList(articles),
              ),
            ),
          ),

          const SizedBox(height: 24),
          _buildSectionHeader('Custom Empty Widget'),
          SizedBox(
            height: 200,
            child: Card(
              child: EntityStateWidget<List<Comment>>(
                model: controller.commentsState,
                onRetry: () => controller.loadComments(isRefresh: true),
                // Custom empty builder
                emptyBuilder: (message) => Container(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: BorderRadius.circular(60),
                        ),
                        child: Icon(
                          Icons.chat_bubble_outline,
                          size: 40,
                          color: Colors.blue[400],
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Start the Conversation!',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        message ?? 'No comments available',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: () {
                          // Could open comment dialog
                        },
                        icon: const Icon(Icons.add_comment),
                        label: const Text('Add Comment'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue[600],
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                itemBuilder: (comments) => _buildCommentsList(comments),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Tab 3: CombinedLoadingStateWidget example
  Widget _buildCombinedLoadingTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Combined Loading State'),
          Text(
            'This demonstrates CombinedLoadingStateWidget which shows a loading overlay '
            'when ANY of the provided ViewModels is in loading state.',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),

          // Combined loading widget wrapping multiple EntityStateWidgets
          SizedBox(
            height: 500,
            child: Card(
              child: CombinedLoadingStateWidget(
                viewModels: [
                  controller.userProfileState,
                  controller.articlesState,
                  controller.settingsState,
                ],
                loadingMessage: "Loading dashboard data...",
                // Custom loading widget
                loadingWidget: Container(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          SizedBox(
                            width: 60,
                            height: 60,
                            child: CircularProgressIndicator(
                              strokeWidth: 4,
                              color: Colors.blue[600],
                            ),
                          ),
                          Icon(
                            Icons.dashboard,
                            color: Colors.blue[300],
                            size: 28,
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'Loading Dashboard',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Fetching profile, articles, and settings...',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 16),
                      LinearProgressIndicator(
                        backgroundColor: Colors.grey[200],
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
                      ),
                    ],
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // User profile section
                      Expanded(
                        flex: 2,
                        child: EntityStateWidget<UserProfile>(
                          model: controller.userProfileState,
                          onRetry: () => controller.loadUserProfile(),
                          itemBuilder: (profile) => _buildUserProfileCard(profile),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Articles section
                      Expanded(
                        flex: 3,
                        child: EntityStateWidget<List<Article>>(
                          model: controller.articlesState,
                          onRetry: () => controller.loadArticles(),
                          emptyMessage: "No articles available",
                          itemBuilder: (articles) => _buildArticlesList(articles),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Settings section
                      Expanded(
                        flex: 1,
                        child: EntityStateWidget<Map<String, dynamic>>(
                          model: controller.settingsState,
                          onRetry: () => controller.loadSettings(),
                          emptyMessage: "No settings configured",
                          itemBuilder: (settings) => _buildSettingsCard(settings),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),
          Text(
            'Notice: The entire content above is hidden behind a loading overlay '
            'until ALL ViewModels finish loading (success or error).',
            style: TextStyle(
              color: Colors.orange[700],
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  /// Tab 4: Search functionality example
  Widget _buildSearchTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Search Articles'),

          // Search input
          TextField(
            decoration: InputDecoration(
              hintText: 'Search for articles...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            onSubmitted: (query) => controller.searchArticles(query),
          ),

          const SizedBox(height: 16),

          // Search results
          Expanded(
            child: Card(
              child: EntityStateWidget<List<Article>>(
                model: controller.searchResultsState,
                onRetry: () => controller.searchArticles(controller.searchQuery.value),
                emptyMessage: controller.searchQuery.value.isEmpty
                    ? "Enter a search term to find articles"
                    : "No articles found for '${controller.searchQuery.value}'",
                emptyIcon: Icon(
                  controller.searchQuery.value.isEmpty
                      ? Icons.search_outlined
                      : Icons.search_off_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                itemBuilder: (articles) => _buildSearchResultsList(articles),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for building UI components

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
    );
  }

  Widget _buildUserProfileCard(UserProfile profile) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Avatar
          CircleAvatar(
            radius: 30,
            backgroundColor: Colors.blue[100],
            child: Text(
              profile.name.substring(0, 1).toUpperCase(),
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.blue[700],
              ),
            ),
          ),
          const SizedBox(width: 16),

          // Profile info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  profile.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  profile.email,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildStatChip('Followers', profile.followersCount),
                    const SizedBox(width: 8),
                    _buildStatChip('Following', profile.followingCount),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatChip(String label, int count) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '$count $label',
        style: TextStyle(
          fontSize: 12,
          color: Colors.blue[700],
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildArticlesList(List<Article> articles) {
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: articles.length,
      itemBuilder: (context, index) {
        final article = articles[index];
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.orange[100],
              child: Icon(
                Icons.article,
                color: Colors.orange[700],
              ),
            ),
            title: Text(
              article.title,
              style: const TextStyle(fontWeight: FontWeight.w500),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(
                  'By ${article.author}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  DateFormat('MMM dd, yyyy').format(article.publishedAt),
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 11,
                  ),
                ),
              ],
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.favorite_outline,
                  size: 16,
                  color: Colors.red[300],
                ),
                const SizedBox(height: 2),
                Text(
                  '${article.likesCount}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCommentsList(List<Comment> comments) {
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: comments.length,
      itemBuilder: (context, index) {
        final comment = comments[index];
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.green[100],
                      child: Text(
                        comment.author.substring(0, 1).toUpperCase(),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.green[700],
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            comment.author,
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                            ),
                          ),
                          Text(
                            DateFormat('MMM dd, HH:mm').format(comment.createdAt),
                            style: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 11,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  comment.content,
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSearchResultsList(List<Article> articles) {
    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: articles.length,
      itemBuilder: (context, index) {
        final article = articles[index];
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.purple[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.search,
                color: Colors.purple[600],
                size: 20,
              ),
            ),
            title: Text(
              article.title,
              style: const TextStyle(fontWeight: FontWeight.w500),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(
                  article.content,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  'By ${article.author} • ${DateFormat('MMM dd').format(article.publishedAt)}',
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 11,
                  ),
                ),
              ],
            ),
            trailing: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey[400],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSettingsCard(Map<String, dynamic> settings) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.settings,
                color: Colors.grey[600],
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Settings',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              childAspectRatio: 3,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              children: settings.entries.map((entry) {
                return Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        entry.key,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        entry.value.toString(),
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}

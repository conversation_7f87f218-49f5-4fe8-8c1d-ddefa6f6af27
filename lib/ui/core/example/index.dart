/// EntityStateWidget & CombinedLoadingStateWidget Examples
/// 
/// This package provides comprehensive examples demonstrating how to use
/// EntityStateWidget and CombinedLoadingStateWidget in Flutter applications.
/// 
/// ## Quick Start
/// 
/// ```dart
/// import 'package:darve/ui/core/example/index.dart';
/// 
/// // Launch examples from anywhere in your app
/// DemoLauncher.launchExamples();
/// 
/// // Or add to your routes
/// GetPage(
///   name: '/examples',
///   page: () => const ExamplePage(),
///   binding: ExampleBinding(),
/// )
/// ```
/// 
/// ## Features Demonstrated
/// 
/// - ✅ All 4 widget states (loading, content, empty, error)
/// - ✅ Custom builders for each state
/// - ✅ Multiple data types (objects, lists, paginated data)
/// - ✅ CombinedLoadingStateWidget coordination
/// - ✅ Error handling with retry functionality
/// - ✅ Search functionality implementation
/// - ✅ Network error simulation
/// - ✅ Real-world usage patterns
/// 
/// ## Example Structure
/// 
/// ```
/// lib/ui/core/example/
/// ├── example_controller.dart    # Controller with multiple ViewModels
/// ├── example_page.dart         # UI showcasing all features
/// ├── example_binding.dart      # GetX dependency injection
/// ├── demo_launcher.dart        # Easy integration helpers
/// ├── README.md                 # User documentation
/// ├── TECHNICAL_GUIDE.md        # Technical implementation guide
/// └── index.dart               # This file - package exports
/// ```

library entity_state_widget_examples;

// Core example files
export 'example_controller.dart';
export 'example_page.dart';
export 'example_binding.dart';
export 'demo_launcher.dart';

// Re-export the main widgets for convenience
export '../entity_state_widget.dart';
export '../combined_loading_state_widget.dart';

/// Example model classes for demonstration purposes
/// These are included in the example_controller.dart file:
/// - UserProfile: Demonstrates single object ViewModels
/// - Article: Demonstrates list ViewModels  
/// - Comment: Demonstrates paginated ViewModels

/// Key Classes Overview:
/// 
/// ## ExampleController
/// Comprehensive controller demonstrating:
/// - Multiple ViewModel types (single, list, paginated)
/// - Proper state transitions (loading → content/empty/error)
/// - Error simulation for testing
/// - Search functionality
/// - Retry mechanisms
/// 
/// ## ExamplePage  
/// UI page with 4 tabs showcasing:
/// - Basic EntityStateWidget usage
/// - Custom builders for all states
/// - CombinedLoadingStateWidget coordination
/// - Search implementation
/// 
/// ## DemoLauncher
/// Utility class providing easy integration:
/// - Launch methods for navigation
/// - Pre-built UI components (buttons, cards, list tiles)
/// - Extension methods for GetX
/// 
/// ## Usage Patterns
/// 
/// ### 1. Basic EntityStateWidget
/// ```dart
/// EntityStateWidget<UserProfile>(
///   model: controller.userProfileState,
///   onRetry: () => controller.loadUserProfile(),
///   emptyMessage: "No profile available",
///   itemBuilder: (profile) => ProfileCard(profile: profile),
/// )
/// ```
/// 
/// ### 2. Custom State Builders
/// ```dart
/// EntityStateWidget<List<Article>>(
///   model: controller.articlesState,
///   loadingBuilder: () => CustomLoadingWidget(),
///   errorBuilder: (error, onRetry) => CustomErrorWidget(error, onRetry),
///   emptyBuilder: (message) => CustomEmptyWidget(message),
///   itemBuilder: (articles) => ArticlesList(articles: articles),
/// )
/// ```
/// 
/// ### 3. Combined Loading States
/// ```dart
/// CombinedLoadingStateWidget(
///   viewModels: [
///     controller.profileState,
///     controller.articlesState,
///     controller.settingsState,
///   ],
///   loadingMessage: "Loading dashboard...",
///   child: DashboardContent(),
/// )
/// ```
/// 
/// ### 4. Integration Helpers
/// ```dart
/// // Add to your app menu
/// DemoLauncher.createDemoListTile()
/// 
/// // Add as dashboard card
/// DemoLauncher.createDemoCard()
/// 
/// // Direct navigation
/// DemoLauncher.launchExamples()
/// ```
/// 
/// ## State Management Flow
/// 
/// The examples demonstrate the complete state management flow:
/// 
/// ```
/// Initial State (loading)
///        ↓
/// API Call / Data Fetch
///        ↓
/// ┌─────────────┬─────────────┬─────────────┐
/// │   Success   │   Failure   │    Empty    │
/// │  (content)  │   (error)   │   (empty)   │
/// └─────────────┴─────────────┴─────────────┘
///                      ↓
///                 Retry Action
///                      ↓
///               Back to Loading
/// ```
/// 
/// ## Error Handling
/// 
/// The examples integrate with the app's error system:
/// - AppError objects with severity levels
/// - User-friendly error messages
/// - Contextual error icons and colors
/// - Retry mechanisms for recovery
/// 
/// ## Best Practices Demonstrated
/// 
/// 1. **Consistent State Transitions**: Proper loading → result flows
/// 2. **User-Friendly Messaging**: Clear, actionable error and empty states
/// 3. **Retry Mechanisms**: Always provide recovery options
/// 4. **Type Safety**: Strongly typed ViewModels
/// 5. **Performance**: Efficient reactive updates with GetX
/// 6. **Customization**: Flexible builders for different use cases
/// 7. **Testing**: Mockable controllers and state simulation
/// 
/// For detailed implementation guides, see:
/// - README.md - User-friendly documentation
/// - TECHNICAL_GUIDE.md - Technical implementation details

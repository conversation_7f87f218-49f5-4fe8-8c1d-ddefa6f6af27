# EntityStateWidget & CombinedLoadingStateWidget Examples

This directory contains comprehensive examples demonstrating how to use `EntityStateWidget` and `CombinedLoadingStateWidget` in your Flutter applications.

## 📁 Files Overview

- **`example_controller.dart`** - Controller with multiple ViewModels demonstrating different data types and scenarios
- **`example_page.dart`** - UI page showcasing all widget features across 4 tabs
- **`example_binding.dart`** - GetX binding for dependency injection
- **`README.md`** - This documentation file

## 🚀 Quick Start

To see the examples in action:

1. Add the route to your app's routing configuration
2. Navigate to the example page
3. Explore the 4 different tabs to see various features

```dart
// In your routes configuration
GetPage(
  name: '/example',
  page: () => const ExamplePage(),
  binding: ExampleBinding(),
),
```

## 📱 Example Features

### Tab 1: Basic Examples
- **Single Object**: User profile data with loading, content, empty, and error states
- **List Data**: Articles list with custom empty messages and icons
- **Paginated Data**: Comments with pagination support

### Tab 2: Custom Builders
- **Custom Loading Widget**: Enhanced loading UI with icons and progress indicators
- **Custom Error Widget**: Professional error handling with action buttons
- **Custom Empty Widget**: Engaging empty states with call-to-action buttons

### Tab 3: Combined Loading
- **CombinedLoadingStateWidget**: Demonstrates loading overlay for multiple ViewModels
- **Dashboard Layout**: Shows how to coordinate multiple data sources
- **Custom Loading Overlay**: Enhanced loading experience with progress indicators

### Tab 4: Search Functionality
- **Search Implementation**: Real-time search with proper state management
- **Dynamic Empty States**: Context-aware empty messages based on search state
- **Error Handling**: Network error simulation for search operations

## 🎛️ Demo Controls

The example includes demo controls accessible via the app bar menu:

- **Toggle Network Error**: Simulate network failures to see error states
- **Toggle Empty Data**: Simulate empty responses to see empty states  
- **Reset & Reload**: Reset all states and reload data

## 💡 Key Learning Points

### 1. ViewModel State Management

```dart
// Define reactive ViewModels in your controller
final Rx<ViewModel<UserProfile>> userProfileState = 
    const ViewModel<UserProfile>.loading().obs;

// Update states based on operations
Future<void> loadUserProfile() async {
  try {
    userProfileState.value = const ViewModel<UserProfile>.loading();
    final profile = await repository.getUserProfile();
    userProfileState.value = ViewModel<UserProfile>.content(profile);
  } catch (e) {
    userProfileState.value = ViewModel<UserProfile>.error(e);
  }
}
```

### 2. EntityStateWidget Usage

```dart
EntityStateWidget<UserProfile>(
  model: controller.userProfileState,
  onRetry: () => controller.loadUserProfile(),
  emptyMessage: "No user profile available",
  emptyIcon: const Icon(Icons.person_outline, size: 64),
  itemBuilder: (profile) => UserProfileCard(profile: profile),
)
```

### 3. Custom State Builders

```dart
EntityStateWidget<List<Article>>(
  model: controller.articlesState,
  loadingBuilder: () => CustomLoadingWidget(),
  errorBuilder: (error, onRetry) => CustomErrorWidget(error, onRetry),
  emptyBuilder: (message) => CustomEmptyWidget(message),
  itemBuilder: (articles) => ArticlesList(articles: articles),
)
```

### 4. Combined Loading States

```dart
CombinedLoadingStateWidget(
  viewModels: [
    controller.userProfileState,
    controller.articlesState,
    controller.settingsState,
  ],
  loadingMessage: "Loading dashboard data...",
  child: DashboardContent(),
)
```

## 🔧 Customization Examples

### Custom Loading Widget
The example shows how to create engaging loading states with:
- Animated progress indicators
- Contextual icons
- Descriptive messages
- Progress bars

### Custom Error Widget  
Professional error handling with:
- Severity-based icons and colors
- Action buttons (Retry, Details)
- User-friendly error messages
- Proper error categorization

### Custom Empty Widget
Engaging empty states featuring:
- Contextual illustrations
- Call-to-action buttons
- Encouraging messaging
- Brand-consistent styling

## 📊 Data Types Demonstrated

1. **Single Objects**: User profiles, settings
2. **Lists**: Articles, search results
3. **Paginated Lists**: Comments with pagination metadata
4. **Maps**: Configuration/settings data

## 🎯 Best Practices Shown

1. **State Transitions**: Proper loading → content/empty/error flows
2. **Error Handling**: User-friendly error messages with retry options
3. **Empty States**: Contextual and actionable empty state messaging
4. **Loading States**: Informative loading indicators with progress feedback
5. **Retry Logic**: Consistent retry mechanisms across all states
6. **Type Safety**: Strongly typed ViewModels for different data types

## 🔄 State Flow Examples

```
Loading State → API Call → Success → Content State
                      ↘ Failure → Error State → Retry → Loading State
                      ↘ Empty Response → Empty State
```

## 🚨 Error Simulation

The example includes error simulation to demonstrate:
- Network connectivity issues
- API server errors  
- Empty data responses
- Timeout scenarios

Use the demo controls to trigger these scenarios and see how the widgets handle them gracefully.

## 📝 Usage Tips

1. **Always provide onRetry**: Users should be able to recover from errors
2. **Customize empty states**: Make them contextual and actionable
3. **Use appropriate icons**: Visual cues help users understand states
4. **Provide loading feedback**: Users should know something is happening
5. **Handle all states**: Don't forget edge cases like empty data

This example serves as a comprehensive reference for implementing robust state management in your Flutter applications using EntityStateWidget and CombinedLoadingStateWidget.

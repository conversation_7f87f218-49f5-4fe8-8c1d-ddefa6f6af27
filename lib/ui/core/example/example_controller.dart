import 'package:darve/ui/core/entity_state_widget.dart';
import 'package:darve/services/error/error_models.dart';
import 'package:get/get.dart';

/// Example model classes for demonstration
class UserProfile {
  final String id;
  final String name;
  final String email;
  final String avatarUrl;
  final int followersCount;
  final int followingCount;

  UserProfile({
    required this.id,
    required this.name,
    required this.email,
    required this.avatarUrl,
    required this.followersCount,
    required this.followingCount,
  });
}

class Article {
  final String id;
  final String title;
  final String content;
  final String author;
  final DateTime publishedAt;
  final int likesCount;

  Article({
    required this.id,
    required this.title,
    required this.content,
    required this.author,
    required this.publishedAt,
    required this.likesCount,
  });
}

class Comment {
  final String id;
  final String content;
  final String author;
  final DateTime createdAt;

  Comment({
    required this.id,
    required this.content,
    required this.author,
    required this.createdAt,
  });
}

/// Comprehensive example controller demonstrating EntityStateWidget and CombinedLoadingStateWidget
class ExampleController extends GetxController {
  // Single object ViewModels
  final Rx<ViewModel<UserProfile>> userProfileState =
      const ViewModel<UserProfile>.loading().obs;

  // List ViewModels
  final Rx<ViewModel<List<Article>>> articlesState =
      const ViewModel<List<Article>>.loading().obs;

  // Paginated ViewModels
  final Rx<PaginatedViewModel<Comment>> commentsState =
      const PaginatedViewModel<Comment>.loading().obs;

  // Search ViewModels
  final Rx<ViewModel<List<Article>>> searchResultsState =
      const ViewModel<List<Article>>.empty().obs;

  // Settings/Configuration ViewModels
  final Rx<ViewModel<Map<String, dynamic>>> settingsState =
      const ViewModel<Map<String, dynamic>>.loading().obs;

  // Demo control variables
  final RxBool simulateNetworkError = false.obs;
  final RxBool simulateEmptyData = false.obs;
  final RxString searchQuery = ''.obs;

  @override
  void onInit() {
    super.onInit();
    // Auto-load initial data
    loadAllData();
  }

  /// Load all data for CombinedLoadingStateWidget demonstration
  Future<void> loadAllData() async {
    await Future.wait([
      loadUserProfile(),
      loadArticles(),
      loadComments(),
      loadSettings(),
    ]);
  }

  /// Load user profile data
  Future<void> loadUserProfile() async {
    try {
      userProfileState.value = const ViewModel<UserProfile>.loading();

      // Simulate network delay
      await Future.delayed(const Duration(seconds: 2));

      if (simulateNetworkError.value) {
        throw NetworkError(
          message: 'Failed to load user profile. Please check your connection.',
          code: 'NETWORK_ERROR',
        );
      }

      if (simulateEmptyData.value) {
        userProfileState.value = const ViewModel<UserProfile>.empty();
        return;
      }

      // Simulate successful data load
      final profile = UserProfile(
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        avatarUrl: 'https://via.placeholder.com/150',
        followersCount: 1234,
        followingCount: 567,
      );

      userProfileState.value = ViewModel<UserProfile>.content(profile);
    } catch (e) {
      userProfileState.value = ViewModel<UserProfile>.error(e);
    }
  }

  /// Load articles list
  Future<void> loadArticles() async {
    try {
      articlesState.value = const ViewModel<List<Article>>.loading();

      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 1500));

      if (simulateNetworkError.value) {
        throw const ApiError(
          message: 'Unable to fetch articles at the moment.',
          code: 'ARTICLES_FETCH_ERROR',
        );
      }

      if (simulateEmptyData.value) {
        articlesState.value = const ViewModel<List<Article>>.empty();
        return;
      }

      // Simulate successful data load
      final articles = List.generate(5, (index) => Article(
        id: 'article_$index',
        title: 'Article ${index + 1}: Understanding Flutter State Management',
        content: 'This is the content of article ${index + 1}...',
        author: 'Author ${index + 1}',
        publishedAt: DateTime.now().subtract(Duration(days: index)),
        likesCount: (index + 1) * 10,
      ));

      articlesState.value = ViewModel<List<Article>>.content(articles);
    } catch (e) {
      articlesState.value = ViewModel<List<Article>>.error(e);
    }
  }

  /// Load comments with pagination
  Future<void> loadComments({bool isRefresh = false}) async {
    try {
      if (isRefresh) {
        commentsState.value = const PaginatedViewModel<Comment>.loading();
      }

      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 1000));

      if (simulateNetworkError.value) {
        throw UserError(
          message: 'Comments could not be loaded. Please try again.',
          code: 'COMMENTS_LOAD_ERROR',
          severity: ErrorSeverity.warning,
        );
      }

      if (simulateEmptyData.value) {
        commentsState.value = const PaginatedViewModel<Comment>.empty();
        return;
      }

      // Simulate successful data load
      final comments = List.generate(3, (index) => Comment(
        id: 'comment_$index',
        content: 'This is comment ${index + 1}. Great article!',
        author: 'Commenter ${index + 1}',
        createdAt: DateTime.now().subtract(Duration(hours: index)),
      ));

      commentsState.value = PaginatedViewModel<Comment>.content(
        comments,
        currentPage: 1,
        hasMore: true,
      );
    } catch (e) {
      commentsState.value = PaginatedViewModel<Comment>.error(e);
    }
  }

  /// Search articles
  Future<void> searchArticles(String query) async {
    try {
      searchQuery.value = query;

      if (query.trim().isEmpty) {
        searchResultsState.value = const ViewModel<List<Article>>.empty();
        return;
      }

      searchResultsState.value = const ViewModel<List<Article>>.loading();

      // Simulate search delay
      await Future.delayed(const Duration(milliseconds: 800));

      if (simulateNetworkError.value) {
        throw NetworkError(
          message: 'Search failed. Please check your connection and try again.',
          code: 'SEARCH_ERROR',
        );
      }

      // Simulate search results
      final results = List.generate(2, (index) => Article(
        id: 'search_result_$index',
        title: 'Search Result ${index + 1}: $query',
        content: 'This article matches your search for "$query"...',
        author: 'Search Author ${index + 1}',
        publishedAt: DateTime.now().subtract(Duration(days: index + 10)),
        likesCount: (index + 1) * 5,
      ));

      if (results.isEmpty) {
        searchResultsState.value = const ViewModel<List<Article>>.empty();
      } else {
        searchResultsState.value = ViewModel<List<Article>>.content(results);
      }
    } catch (e) {
      searchResultsState.value = ViewModel<List<Article>>.error(e);
    }
  }

  /// Load settings/configuration
  Future<void> loadSettings() async {
    try {
      settingsState.value = const ViewModel<Map<String, dynamic>>.loading();

      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 500));

      if (simulateNetworkError.value) {
        throw const ApiError(
          message: 'Settings could not be loaded.',
          code: 'SETTINGS_ERROR',
        );
      }

      // Simulate successful settings load
      final settings = {
        'theme': 'light',
        'notifications': true,
        'language': 'en',
        'autoSave': true,
      };

      settingsState.value = ViewModel<Map<String, dynamic>>.content(settings);
    } catch (e) {
      settingsState.value = ViewModel<Map<String, dynamic>>.error(e);
    }
  }

  /// Toggle network error simulation
  void toggleNetworkError() {
    simulateNetworkError.value = !simulateNetworkError.value;
  }

  /// Toggle empty data simulation
  void toggleEmptyData() {
    simulateEmptyData.value = !simulateEmptyData.value;
  }

  /// Reset all states to loading and reload
  Future<void> resetAndReload() async {
    userProfileState.value = const ViewModel<UserProfile>.loading();
    articlesState.value = const ViewModel<List<Article>>.loading();
    commentsState.value = const PaginatedViewModel<Comment>.loading();
    searchResultsState.value = const ViewModel<List<Article>>.empty();
    settingsState.value = const ViewModel<Map<String, dynamic>>.loading();

    await loadAllData();
  }

  /// Convenience getters for accessing data
  UserProfile? get userProfile => userProfileState.value.data;
  List<Article> get articles => articlesState.value.data ?? [];
  List<Comment> get comments => commentsState.value.data ?? [];
  List<Article> get searchResults => searchResultsState.value.data ?? [];
  Map<String, dynamic> get settings => settingsState.value.data ?? {};
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'example_page.dart';
import 'example_binding.dart';

/// Demo launcher for EntityStateWidget examples
/// 
/// This provides an easy way to launch the examples from anywhere in the app.
/// You can add this to your main menu, debug menu, or developer tools.
class DemoLauncher {
  /// Launch the EntityStateWidget examples page
  static void launchExamples() {
    Get.to(
      () => const ExamplePage(),
      binding: ExampleBinding(),
      transition: Transition.cupertino,
      duration: const Duration(milliseconds: 300),
    );
  }

  /// Create a demo button widget that can be placed anywhere
  static Widget createDemoButton({
    String? label,
    IconData? icon,
    Color? color,
  }) {
    return ElevatedButton.icon(
      onPressed: launchExamples,
      icon: Icon(icon ?? Icons.widgets),
      label: Text(label ?? 'EntityStateWidget Examples'),
      style: ElevatedButton.styleFrom(
        backgroundColor: color ?? Colors.blue[600],
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  /// Create a floating action button for quick access
  static Widget createDemoFAB() {
    return FloatingActionButton.extended(
      onPressed: launchExamples,
      icon: const Icon(Icons.widgets),
      label: const Text('Examples'),
      backgroundColor: Colors.blue[600],
      foregroundColor: Colors.white,
    );
  }

  /// Create a list tile for use in drawers or menus
  static Widget createDemoListTile() {
    return ListTile(
      leading: Icon(
        Icons.widgets,
        color: Colors.blue[600],
      ),
      title: const Text('EntityStateWidget Examples'),
      subtitle: const Text('View comprehensive widget examples'),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: launchExamples,
    );
  }

  /// Create a card widget for dashboard or home screen
  static Widget createDemoCard() {
    return Card(
      margin: const EdgeInsets.all(8),
      child: InkWell(
        onTap: launchExamples,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.widgets,
                      color: Colors.blue[600],
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Widget Examples',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey[400],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Explore EntityStateWidget and CombinedLoadingStateWidget examples with different states, custom builders, and real-world scenarios.',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: [
                  _buildFeatureChip('4 States'),
                  _buildFeatureChip('Custom Builders'),
                  _buildFeatureChip('Error Handling'),
                  _buildFeatureChip('Search Demo'),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Widget _buildFeatureChip(String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 11,
          color: Colors.blue[700],
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}

/// Extension to add demo launcher to GetX navigation
extension DemoLauncherExtension on GetInterface {
  /// Quick access to launch examples
  void toExamples() => DemoLauncher.launchExamples();
}

/// Usage Examples:
/// 
/// 1. In a menu or drawer:
/// ```dart
/// DemoLauncher.createDemoListTile()
/// ```
/// 
/// 2. As a button:
/// ```dart
/// DemoLauncher.createDemoButton(
///   label: 'View Examples',
///   icon: Icons.play_arrow,
///   color: Colors.green,
/// )
/// ```
/// 
/// 3. As a floating action button:
/// ```dart
/// floatingActionButton: DemoLauncher.createDemoFAB()
/// ```
/// 
/// 4. As a dashboard card:
/// ```dart
/// DemoLauncher.createDemoCard()
/// ```
/// 
/// 5. Direct navigation:
/// ```dart
/// DemoLauncher.launchExamples();
/// // or using extension
/// Get.toExamples();
/// ```

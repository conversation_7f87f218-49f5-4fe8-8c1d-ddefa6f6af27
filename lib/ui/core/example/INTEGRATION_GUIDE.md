# Integration Guide: Adding EntityStateWidget Examples to Your App

This guide shows how to integrate the comprehensive EntityStateWidget examples into your Flutter application.

## 🚀 Quick Integration

### Option 1: Add to App Routes (Recommended)

Add the example route to your app's routing configuration:

```dart
// In your routes file (e.g., app_routes.dart)
import 'package:darve/ui/core/example/index.dart';

class AppRoutes {
  static const String examples = '/examples';
  // ... other routes
}

// In your GetX pages configuration
List<GetPage> getPages() => [
  // ... other pages
  GetPage(
    name: AppRoutes.examples,
    page: () => const ExamplePage(),
    binding: ExampleBinding(),
  ),
];
```

### Option 2: Direct Navigation

Navigate directly from anywhere in your app:

```dart
import 'package:darve/ui/core/example/index.dart';

// Direct navigation
DemoLauncher.launchExamples();

// Or using the GetX extension
Get.toExamples();
```

## 🎨 UI Integration Options

### 1. Add to Main Menu/Drawer

```dart
import 'package:darve/ui/core/example/index.dart';

Drawer(
  child: ListView(
    children: [
      // ... other menu items
      DemoLauncher.createDemoListTile(),
    ],
  ),
)
```

### 2. Add to Dashboard/Home Screen

```dart
import 'package:darve/ui/core/example/index.dart';

Column(
  children: [
    // ... other dashboard items
    DemoLauncher.createDemoCard(),
  ],
)
```

### 3. Add as Floating Action Button

```dart
import 'package:darve/ui/core/example/index.dart';

Scaffold(
  // ... other scaffold properties
  floatingActionButton: DemoLauncher.createDemoFAB(),
)
```

### 4. Add as Custom Button

```dart
import 'package:darve/ui/core/example/index.dart';

DemoLauncher.createDemoButton(
  label: 'View Widget Examples',
  icon: Icons.play_arrow,
  color: Colors.green,
)
```

## 🔧 Development Integration

### For Development/Debug Menu

```dart
// In your debug/developer menu
class DeveloperMenu extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // ... other debug options
        ListTile(
          title: Text('Widget Examples'),
          subtitle: Text('EntityStateWidget & CombinedLoadingStateWidget'),
          leading: Icon(Icons.widgets),
          onTap: () => DemoLauncher.launchExamples(),
        ),
      ],
    );
  }
}
```

### For Testing/QA

```dart
// Add to your testing utilities
class TestingUtils {
  static void openWidgetExamples() {
    DemoLauncher.launchExamples();
  }
  
  static Widget createExamplesButton() {
    return DemoLauncher.createDemoButton(
      label: 'Test Widget States',
      icon: Icons.bug_report,
      color: Colors.orange,
    );
  }
}
```

## 📱 Platform-Specific Integration

### iOS-Style Integration

```dart
CupertinoNavigationBar(
  trailing: CupertinoButton(
    padding: EdgeInsets.zero,
    child: Icon(CupertinoIcons.lab_flask),
    onPressed: () => DemoLauncher.launchExamples(),
  ),
)
```

### Material Design Integration

```dart
AppBar(
  actions: [
    IconButton(
      icon: Icon(Icons.widgets),
      tooltip: 'Widget Examples',
      onPressed: () => DemoLauncher.launchExamples(),
    ),
  ],
)
```

## 🎯 Conditional Integration

### Show Only in Debug Mode

```dart
import 'package:flutter/foundation.dart';
import 'package:darve/ui/core/example/index.dart';

Widget buildMenu() {
  return Column(
    children: [
      // ... regular menu items
      
      // Only show in debug mode
      if (kDebugMode) ...[
        Divider(),
        DemoLauncher.createDemoListTile(),
      ],
    ],
  );
}
```

### Show Only for Developers

```dart
// Assuming you have a user role system
Widget buildDeveloperTools() {
  return Visibility(
    visible: currentUser.isDeveloper,
    child: Card(
      child: Column(
        children: [
          ListTile(
            title: Text('Developer Tools'),
            leading: Icon(Icons.build),
          ),
          DemoLauncher.createDemoListTile(),
        ],
      ),
    ),
  );
}
```

## 🔗 Deep Linking Integration

### Add Deep Link Support

```dart
// In your route configuration
GetPage(
  name: '/examples/:tab',
  page: () => const ExamplePage(),
  binding: ExampleBinding(),
),

// Usage
Get.toNamed('/examples/basic');    // Opens basic tab
Get.toNamed('/examples/custom');   // Opens custom builders tab
Get.toNamed('/examples/combined'); // Opens combined loading tab
Get.toNamed('/examples/search');   // Opens search tab
```

### URL Parameters

```dart
// Enhanced example page with URL parameter support
class ExamplePage extends GetView<ExampleController> {
  const ExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    final initialTab = _getInitialTabFromRoute();
    
    return DefaultTabController(
      initialIndex: initialTab,
      length: 4,
      child: // ... rest of implementation
    );
  }
  
  int _getInitialTabFromRoute() {
    final tab = Get.parameters['tab'];
    switch (tab) {
      case 'basic': return 0;
      case 'custom': return 1;
      case 'combined': return 2;
      case 'search': return 3;
      default: return 0;
    }
  }
}
```

## 📊 Analytics Integration

### Track Example Usage

```dart
// Enhanced demo launcher with analytics
class DemoLauncher {
  static void launchExamples({String? source}) {
    // Track analytics
    Analytics.track('examples_opened', {
      'source': source ?? 'unknown',
      'timestamp': DateTime.now().toIso8601String(),
    });
    
    Get.to(
      () => const ExamplePage(),
      binding: ExampleBinding(),
    );
  }
}

// Usage with source tracking
DemoLauncher.launchExamples(source: 'main_menu');
DemoLauncher.launchExamples(source: 'dashboard');
DemoLauncher.launchExamples(source: 'fab');
```

## 🧪 Testing Integration

### Widget Tests

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:darve/ui/core/example/index.dart';

void main() {
  group('Example Integration Tests', () {
    testWidgets('should launch examples from demo launcher', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DemoLauncher.createDemoButton(),
          ),
        ),
      );
      
      await tester.tap(find.text('EntityStateWidget Examples'));
      await tester.pumpAndSettle();
      
      expect(find.byType(ExamplePage), findsOneWidget);
    });
  });
}
```

### Integration Tests

```dart
import 'package:integration_test/integration_test.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('Examples Integration', () {
    testWidgets('should navigate through all example tabs', (tester) async {
      // Launch app
      await tester.pumpWidget(MyApp());
      
      // Navigate to examples
      DemoLauncher.launchExamples();
      await tester.pumpAndSettle();
      
      // Test each tab
      for (int i = 0; i < 4; i++) {
        await tester.tap(find.byType(Tab).at(i));
        await tester.pumpAndSettle();
        
        // Verify tab content loads
        expect(find.byType(EntityStateWidget), findsWidgets);
      }
    });
  });
}
```

## 🚀 Production Considerations

### Remove from Production Builds

```dart
// Use build flavors or environment variables
class AppConfig {
  static bool get showExamples => 
      kDebugMode || 
      const bool.fromEnvironment('SHOW_EXAMPLES', defaultValue: false);
}

// Conditional menu item
if (AppConfig.showExamples)
  DemoLauncher.createDemoListTile(),
```

### Feature Flags

```dart
// Using a feature flag service
Widget buildMenu() {
  return FutureBuilder<bool>(
    future: FeatureFlags.isEnabled('widget_examples'),
    builder: (context, snapshot) {
      if (snapshot.data == true) {
        return DemoLauncher.createDemoListTile();
      }
      return SizedBox.shrink();
    },
  );
}
```

This integration guide provides multiple approaches to add the EntityStateWidget examples to your app, from simple navigation to advanced deep linking and analytics integration.

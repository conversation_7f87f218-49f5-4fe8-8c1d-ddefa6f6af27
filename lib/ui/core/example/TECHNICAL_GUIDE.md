# Technical Guide: EntityStateWidget & CombinedLoadingStateWidget

## 🏗️ Architecture Overview

### EntityStateWidget
A reactive widget that automatically handles 4 distinct UI states based on a `ViewModel<T>`:

```dart
enum ViewState { loading, content, empty, error }
```

### CombinedLoadingStateWidget  
A coordination widget that monitors multiple `ViewModel` instances and shows a loading overlay when ANY of them is in loading state.

## 🔧 Implementation Details

### ViewModel Structure

```dart
class ViewModel<T> {
  final ViewState state;
  final T? data;
  final dynamic error;

  // Constructors for each state
  const ViewModel.loading();
  const ViewModel.content(this.data);
  const ViewModel.empty();
  const ViewModel.error(this.error);
}
```

### PaginatedViewModel Extension

```dart
class PaginatedViewModel<T> extends ViewModel<List<T>> {
  final int currentPage;
  final bool hasMore;
  
  // Additional pagination metadata
}
```

## 📋 EntityStateWidget API Reference

### Required Parameters
- `model: Rx<ViewModel<T>>` - Reactive ViewModel to observe

### Optional Parameters
- `itemBuilder: WidgetCallback<T>?` - Builder for content state
- `loadingBuilder: LoadingWidgetBuilder?` - Custom loading widget
- `emptyBuilder: EmptyWidgetBuilder?` - Custom empty widget  
- `errorBuilder: ErrorWidgetBuilder?` - Custom error widget
- `onRetry: ActionCallBack?` - Retry function for error state
- `emptyMessage: String?` - Custom empty message
- `emptyIcon: Widget?` - Custom empty state icon

### Type Definitions

```dart
typedef WidgetCallback<T> = Widget Function(T data);
typedef EmptyWidgetBuilder = Widget Function(String? message);
typedef ErrorWidgetBuilder = Widget Function(String? message, VoidCallback? onRetry);
typedef LoadingWidgetBuilder = Widget Function();
typedef ActionCallBack = Future Function();
```

## 📋 CombinedLoadingStateWidget API Reference

### Required Parameters
- `viewModels: List<Rx<ViewModel>>` - List of ViewModels to monitor
- `child: Widget` - Content to show when all ViewModels are done loading

### Optional Parameters
- `loadingWidget: Widget?` - Custom loading overlay widget
- `loadingMessage: String?` - Message to display during loading

## 🎯 Usage Patterns

### 1. Basic Usage

```dart
EntityStateWidget<UserProfile>(
  model: controller.userProfileState,
  onRetry: () => controller.loadUserProfile(),
  itemBuilder: (profile) => UserProfileCard(profile: profile),
)
```

### 2. Custom Empty State

```dart
EntityStateWidget<List<Article>>(
  model: controller.articlesState,
  emptyMessage: "No articles published yet",
  emptyIcon: const Icon(Icons.article_outlined, size: 64),
  itemBuilder: (articles) => ArticlesList(articles: articles),
)
```

### 3. Custom Error Handling

```dart
EntityStateWidget<List<Comment>>(
  model: controller.commentsState,
  errorBuilder: (error, onRetry) => CustomErrorWidget(
    error: error,
    onRetry: onRetry,
    title: "Comments Unavailable",
  ),
  itemBuilder: (comments) => CommentsList(comments: comments),
)
```

### 4. Combined Loading

```dart
CombinedLoadingStateWidget(
  viewModels: [
    controller.profileState,
    controller.postsState,
    controller.followersState,
  ],
  loadingMessage: "Loading profile data...",
  child: ProfileDashboard(),
)
```

## 🔄 State Management Flow

### Controller Pattern

```dart
class ExampleController extends GetxController {
  final Rx<ViewModel<UserProfile>> userProfileState = 
      const ViewModel<UserProfile>.loading().obs;

  Future<void> loadUserProfile() async {
    try {
      // 1. Set loading state
      userProfileState.value = const ViewModel<UserProfile>.loading();
      
      // 2. Fetch data
      final profile = await repository.getUserProfile();
      
      // 3. Set success state
      if (profile != null) {
        userProfileState.value = ViewModel<UserProfile>.content(profile);
      } else {
        userProfileState.value = const ViewModel<UserProfile>.empty();
      }
    } catch (e) {
      // 4. Set error state
      userProfileState.value = ViewModel<UserProfile>.error(e);
    }
  }
}
```

### Repository Integration

```dart
// Repository returns Result<T> which converts to ViewModel<T>
Future<void> loadData() async {
  dataState.value = const ViewModel.loading();
  
  final result = await repository.getData();
  dataState.value = result.toViewModel(
    onError: (error) => _handleError(error),
    onSuccess: (data) => _handleSuccess(data),
  );
}
```

## 🎨 Default Widget Implementations

### DefaultLoadingWidget
```dart
Center(
  child: Padding(
    padding: EdgeInsets.all(32.0),
    child: CircularProgressIndicator(),
  ),
)
```

### DefaultEmptyWidget
```dart
Center(
  child: Column(
    children: [
      Icon(Icons.inbox_outlined, size: 64, color: Colors.grey),
      SizedBox(height: 16),
      Text(message, style: TextStyle(color: Colors.grey)),
    ],
  ),
)
```

### DefaultErrorWidget
```dart
Center(
  child: Column(
    children: [
      Icon(_getErrorIcon(), size: 64, color: _getErrorColor()),
      SizedBox(height: 16),
      Text(_getUserMessage()),
      if (onRetry != null) ...[
        SizedBox(height: 24),
        ElevatedButton.icon(
          onPressed: onRetry,
          icon: Icon(Icons.refresh),
          label: Text('Retry'),
        ),
      ],
    ],
  ),
)
```

## 🚨 Error Handling Integration

### AppError Support
The widgets integrate with the app's error system:

```dart
// Error types are automatically handled
if (error is AppError) {
  // Uses AppError.message and severity for display
  return _buildAppErrorWidget(error);
} else if (error is String) {
  // Displays string directly
  return _buildStringErrorWidget(error);
} else {
  // Fallback generic error message
  return _buildGenericErrorWidget();
}
```

### Error Severity Styling
```dart
switch (appError.severity) {
  case ErrorSeverity.warning:
    return Icons.warning_outlined;
  case ErrorSeverity.fatal:
    return Icons.error;
  case ErrorSeverity.expected:
    return Icons.info_outline;
  default:
    return Icons.error_outline;
}
```

## 🔧 Advanced Customization

### Custom Loading with Progress
```dart
loadingBuilder: () => Column(
  children: [
    CircularProgressIndicator(),
    SizedBox(height: 16),
    Text('Loading...'),
    LinearProgressIndicator(),
  ],
)
```

### Contextual Empty States
```dart
emptyBuilder: (message) => Column(
  children: [
    _getContextualIcon(),
    Text(message ?? _getContextualMessage()),
    ElevatedButton(
      onPressed: _getContextualAction(),
      child: Text(_getContextualActionText()),
    ),
  ],
)
```

### Rich Error Display
```dart
errorBuilder: (error, onRetry) => ErrorCard(
  error: error,
  onRetry: onRetry,
  onReport: () => _reportError(error),
  onDismiss: () => _dismissError(),
)
```

## 📊 Performance Considerations

1. **Reactive Updates**: Uses `Obx()` for efficient reactive updates
2. **Widget Rebuilds**: Only rebuilds when ViewModel state changes
3. **Memory Management**: ViewModels are lightweight and garbage collected
4. **Lazy Loading**: Custom builders are only called when needed

## 🧪 Testing Strategies

### Unit Testing ViewModels
```dart
test('should transition from loading to content', () {
  final viewModel = ViewModel<String>.loading();
  expect(viewModel.state, ViewState.loading);
  
  final contentViewModel = ViewModel<String>.content('test');
  expect(contentViewModel.state, ViewState.content);
  expect(contentViewModel.data, 'test');
});
```

### Widget Testing
```dart
testWidgets('should show loading state', (tester) async {
  final controller = MockController();
  when(controller.dataState).thenReturn(
    const ViewModel<String>.loading().obs
  );
  
  await tester.pumpWidget(
    EntityStateWidget<String>(
      model: controller.dataState,
      itemBuilder: (data) => Text(data),
    ),
  );
  
  expect(find.byType(CircularProgressIndicator), findsOneWidget);
});
```

This technical guide provides the foundation for implementing robust state management using these widgets in your Flutter applications.

import 'package:get/get.dart';
import 'example_controller.dart';

/// Binding for ExamplePage
/// 
/// This binding initializes the ExampleController when the ExamplePage is accessed.
/// It demonstrates proper dependency injection patterns for GetX controllers.
class ExampleBinding extends Bindings {
  @override
  void dependencies() {
    // Register ExampleController
    Get.lazyPut<ExampleController>(
      () => ExampleController(),
      fenix: true, // Keep controller alive for demo purposes
    );
  }
}

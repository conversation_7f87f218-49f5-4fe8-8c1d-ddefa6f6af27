import 'package:darve/ui/auth/password/forgot_password_controller.dart';
import 'package:darve/services/providers/auth_provider.dart';
import 'package:darve/routes/route_helper.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/styles.dart';
import 'package:darve/utils/validators.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ForgotPasswordPage extends GetView<ForgotPasswordController> {
  const ForgotPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    final authService = AuthProvider.auth;
    return Scaffold(
      body: Stack(
        children: [
          Container(
            color: Styles.primaryColor,
            height: double.infinity,
            width: double.infinity,
          ),
          Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding: const EdgeInsets.only(top: 20),
              child: SizedBox(
                width: 220,
                child: Image.asset(
                  "assets/images/darve-no-bg.png",
                  fit: BoxFit.fitWidth,
                ),
              ),
            ),
          ),
          Positioned(
            top: 50.0,
            left: 8.0,
            child: IconButton(
              icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
              onPressed: () {
                RouteHelper.goBack();
              },
            ),
          ),
          AnimatedPositioned(
            duration: const Duration(milliseconds: 800),
            curve: Curves.easeOut,
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(50.0),
                  topRight: Radius.circular(50.0),
                ),
                color: Styles.foregroundColor,
              ),
              height: MediaQuery.of(context).size.height * 0.75,
              child: Column(
                children: [
                  const Padding(
                    padding: EdgeInsets.only(top: 32.0, bottom: 2.0),
                    child: Text(
                      "Forgot Password",
                      style: TextStyle(
                        fontSize: 22.0,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Obx(() {
                    // Observe auth service state
                    final authState = authService.authState.value;

                    // Show error if there's one
                    if (authState.error != null) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        ErrorsHandle().displayErrorToast(authState.error);
                        authService.clearError();
                      });
                    }

                    if (controller.isLoading) {
                      return const Padding(
                        padding: EdgeInsets.only(top: 160.0),
                        child: Center(
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }
                    if (controller.isEmailSent.value) {
                      return const Column(
                        children: [
                          SizedBox(height: 16.0),
                          Text(
                            "A reset password link has been sent.",
                            style: TextStyle(
                                color: Styles.textLightColor,
                                fontWeight: FontWeight.w400,
                                fontSize: 14),
                          ),
                        ],
                      );
                    }
                    return Column(
                      children: [
                        const Text(
                          "Trouble with logging in or forgot your password?",
                          style: TextStyle(
                              color: Styles.textLightColor,
                              fontWeight: FontWeight.w400,
                              fontSize: 14),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                              top: 26.0, left: 24.0, right: 24.0),
                          child: Form(
                            key: controller.formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                              Image.asset(
                                "assets/images/sign_in/forgot.png",
                                height: 150,
                              ),
                              const SizedBox(height: 16.0),
                              const Text(
                                  "Enter username or email and you will recieve \nreset password link on your email.",
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      color: Color(0xFF0F141C),
                                      fontWeight: FontWeight.w400,
                                      fontSize: 14)),
                              const SizedBox(height: 38.0),
                              const Text("Username | Email",
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      color: Color(0xFF0F141C),
                                      fontWeight: FontWeight.w400,
                                      fontSize: 14)),
                              const SizedBox(height: 16.0),
                              Obx(() => TextFormField(
                                controller: controller.usernameOrEmailController,
                                validator: Validators.validateEmailOrUsername,
                                enabled: !controller.isSendingResetLink.value,
                                decoration: InputDecoration(
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(16.0),
                                    borderSide: const BorderSide(
                                      color: Styles
                                          .primaryColor, // Your desired color
                                      width: 1.0,
                                    ),
                                  ),
                                  filled: true,
                                  hintText: "Enter your username or email",
                                  hintStyle: const TextStyle(
                                      color: Styles.textLightColor,
                                      fontSize: 15.0),
                                  fillColor: Colors.white,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(16.0),
                                    borderSide: BorderSide.none,
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                      vertical: 15.0, horizontal: 20.0),
                                ),
                                style: const TextStyle(
                                  fontWeight: FontWeight.w400,
                                ),
                              )),
                              const SizedBox(height: 68.0),
                              Center(
                                child: SizedBox(
                                  width:
                                      MediaQuery.of(context).size.width * 0.475,
                                  child: Obx(() => ElevatedButton(
                                    onPressed: controller.isSendingResetLink.value
                                        ? null
                                        : () async {
                                            controller.forgotPassword();
                                          },
                                    child: controller.isSendingResetLink.value
                                        ? const SizedBox(
                                            height: 20,
                                            width: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                            ),
                                          )
                                        : const Text("Continue"),
                                  )),
                                ),
                              ),
                              const SizedBox(height: 56.0),
                              TextButton(
                                onPressed: () {
                                  RouteHelper.goBack();
                                },
                                child: const Text(
                                  "Back to Login",
                                  style: TextStyle(
                                      color: Styles.textLightColor,
                                      fontWeight: FontWeight.w400,
                                      fontSize: 14),
                                ),
                              )
                            ],
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

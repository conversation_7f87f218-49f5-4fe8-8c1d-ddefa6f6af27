import 'package:darve/ui/settings/settings_controller.dart';
import 'package:darve/ui/settings/profile/profile_edit_controller.dart';
import 'package:darve/ui/settings/shared/verify_email_controller.dart';
import 'package:get/get.dart';

class SettingsBinding extends Bindings {
  @override
  void dependencies() {
    // Register SettingsController for search functionality
    Get.lazyPut<SettingsController>(
      () => SettingsController(),
      fenix: true, // Keep alive for settings session
    );

    // Lazily register VerifyEmailController for settings functionality
    Get.lazyPut<VerifyEmailController>(
      () => VerifyEmailController(),
      fenix: true, // Keep alive for settings session
    );
  }
}

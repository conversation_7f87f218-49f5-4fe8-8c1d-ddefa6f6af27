import 'package:darve/api/models/profile_data_model.dart';
import 'package:darve/api/models/user_model.dart';

class ProfileEditFormModel {
  final String fullName;
  final String username;
  final String bio;
  final String email;
  final String mobileNumber;
  final String dateOfBirth;
  final List<String> socialLinks;
  final String? profileImagePath;
  final String? profileImageUrl;

  const ProfileEditFormModel({
    required this.fullName,
    required this.username,
    required this.bio,
    required this.email,
    required this.mobileNumber,
    required this.dateOfBirth,
    required this.socialLinks,
    this.profileImagePath,
    this.profileImageUrl,
  });

  /// Create from ProfileDataModel
  factory ProfileEditFormModel.fromProfileData(ProfileDataModel profile) {
    return ProfileEditFormModel(
      fullName: profile.fullName ?? '',
      username: profile.userName,
      bio: profile.bio ?? '',
      email: profile.emailId ?? '',
      mobileNumber: '', // Mobile number not in ProfileDataModel, will need to be fetched separately
      dateOfBirth: '', // Date of birth not in ProfileDataModel, will need to be fetched separately
      socialLinks: profile.socialLinks ?? [],
      profileImageUrl: profile.imageUri,
    );
  }

  /// Create from UserModel
  factory ProfileEditFormModel.fromUserModel(UserModel user) {
    return ProfileEditFormModel(
      fullName: user.name ?? '',
      username: user.username,
      bio: user.bio ?? '',
      email: user.email ?? '',
      mobileNumber: '', // Mobile number not in UserModel
      dateOfBirth: '', // Date of birth not in UserModel
      socialLinks: [],
      profileImageUrl: user.imageUri,
    );
  }

  /// Create empty form
  factory ProfileEditFormModel.empty() {
    return const ProfileEditFormModel(
      fullName: '',
      username: '',
      bio: '',
      email: '',
      mobileNumber: '',
      dateOfBirth: '',
      socialLinks: [],
    );
  }

  /// Create copy with updated fields
  ProfileEditFormModel copyWith({
    String? fullName,
    String? username,
    String? bio,
    String? email,
    String? mobileNumber,
    String? dateOfBirth,
    List<String>? socialLinks,
    String? profileImagePath,
    String? profileImageUrl,
  }) {
    return ProfileEditFormModel(
      fullName: fullName ?? this.fullName,
      username: username ?? this.username,
      bio: bio ?? this.bio,
      email: email ?? this.email,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      socialLinks: socialLinks ?? this.socialLinks,
      profileImagePath: profileImagePath ?? this.profileImagePath,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
    );
  }

  /// Check if form has any changes compared to original
  bool hasChangesFrom(ProfileEditFormModel original) {
    return fullName != original.fullName ||
           username != original.username ||
           bio != original.bio ||
           email != original.email ||
           mobileNumber != original.mobileNumber ||
           dateOfBirth != original.dateOfBirth ||
           !_listEquals(socialLinks, original.socialLinks) ||
           profileImagePath != null;
  }

  /// Helper method to compare lists
  bool _listEquals(List<String> a, List<String> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  /// Validate form fields
  Map<String, String?> validate() {
    final Map<String, String?> errors = {};

    // Full name validation
    if (fullName.trim().isEmpty) {
      errors['fullName'] = 'Full name is required';
    } else if (fullName.trim().length < 2) {
      errors['fullName'] = 'Full name must be at least 2 characters';
    } else if (fullName.trim().length > 50) {
      errors['fullName'] = 'Full name must be less than 50 characters';
    }

    // Username validation
    if (username.trim().isEmpty) {
      errors['username'] = 'Username is required';
    } else if (username.trim().length < 3) {
      errors['username'] = 'Username must be at least 3 characters';
    } else if (username.trim().length > 30) {
      errors['username'] = 'Username must be less than 30 characters';
    } else if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(username.trim())) {
      errors['username'] = 'Username can only contain letters, numbers, and underscores';
    }

    // Bio validation (optional)
    if (bio.length > 500) {
      errors['bio'] = 'Bio must be less than 500 characters';
    }

    // Email validation
    if (email.trim().isEmpty) {
      errors['email'] = 'Email is required';
    } else if (!RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(email.trim())) {
      errors['email'] = 'Please enter a valid email address';
    }

    // Mobile number validation (optional)
    if (mobileNumber.trim().isNotEmpty) {
      if (!RegExp(r'^\+?[1-9]\d{1,14}$').hasMatch(mobileNumber.trim())) {
        errors['mobileNumber'] = 'Please enter a valid mobile number';
      }
    }

    return errors;
  }

  /// Check if form is valid
  bool get isValid => validate().isEmpty;

  /// Get display image (local path takes precedence over URL)
  String? get displayImage => profileImagePath ?? profileImageUrl;
}


import 'package:darve/ui/core/entity_state_widget.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/result.dart';
import 'package:darve/utils/validators.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MobileVerificationController extends GetxController {
  final TextEditingController mobileController = TextEditingController();
  final TextEditingController confirmMobileController = TextEditingController();
  final RxBool isMobileValid = false.obs;
  final RxBool areMobilesMatching = false.obs;
  final RxBool isVerificationStarted = false.obs;
  final RxBool isVerificationCompleted = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString verificationCode = ''.obs;

  // State management using ViewModel pattern for consistency
  final Rx<ViewModel<bool>> mobileVerificationStartState = const ViewModel<bool>.content(false).obs;
  final Rx<ViewModel<bool>> mobileVerificationConfirmState = const ViewModel<bool>.content(false).obs;

  // Error handling
  final ErrorsHandle _errorHandler = ErrorsHandle();

  @override
  void onInit() {
    super.onInit();

    // Listen to mobile input changes
    mobileController.addListener(_validateMobile);
    confirmMobileController.addListener(_validateMobileMatch);
  }

  @override
  void onClose() {
    mobileController.dispose();
    confirmMobileController.dispose();
    super.onClose();
  }

  /// Reset controller state for new verification
  void resetState() {
    isVerificationStarted.value = false;
    isVerificationCompleted.value = false;
    errorMessage.value = '';
    verificationCode.value = '';
    mobileController.clear();
    confirmMobileController.clear();
  }

  /// Validate mobile number format
  void _validateMobile() {
    final mobile = mobileController.text.trim();
    isMobileValid.value = Validators.validateMobileNumber(mobile) == null;
    _validateMobileMatch();
  }

  /// Validate mobile number confirmation
  void _validateMobileMatch() {
    final mobile = mobileController.text.trim();
    final confirmMobile = confirmMobileController.text.trim();
    areMobilesMatching.value = mobile.isNotEmpty && mobile == confirmMobile;
  }

  /// Start mobile verification process
  Future<void> startMobileVerification() async {
    if (!isMobileValid.value || !areMobilesMatching.value) {
      errorMessage.value = 'Please enter a valid mobile number';
      return;
    }

    // Clear previous errors
    errorMessage.value = '';

    // Set loading state
    mobileVerificationStartState.value = const ViewModel<bool>.loading();

    // TODO: Replace with actual mobile verification API call
    // For now, we'll simulate the API call
    final result = await _simulateMobileVerificationStart(mobileController.text.trim());

    // Use ResultExtensions toViewModel pattern for consistency
    mobileVerificationStartState.value = result.toViewModel(
      onError: (error) {
        errorMessage.value = error.message;
        _errorHandler.displayErrorToast(error, 'mobileVerificationStart');
      },
      onSuccess: (_) {
        // Update state to show verification started
        isVerificationStarted.value = true;
        errorMessage.value = '';

        Get.snackbar(
          'Verification Sent',
          'A verification code has been sent to your mobile number',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );
      },
    );
  }

  /// Confirm mobile verification with code
  Future<void> confirmMobileVerification() async {
    if (verificationCode.value.length != 6) {
      errorMessage.value = 'Please enter the 6-digit verification code';
      return;
    }

    // Clear previous errors
    errorMessage.value = '';

    // Set loading state
    mobileVerificationConfirmState.value = const ViewModel<bool>.loading();

    // TODO: Replace with actual mobile verification confirm API call
    // For now, we'll simulate the API call
    final result = await _simulateMobileVerificationConfirm(
      mobileController.text.trim(),
      verificationCode.value,
    );

    // Use ResultExtensions toViewModel pattern for consistency
    mobileVerificationConfirmState.value = result.toViewModel(
      onError: (error) {
        errorMessage.value = error.message;
        _errorHandler.displayErrorToast(error, 'mobileVerificationConfirm');
      },
      onSuccess: (_) {
        // Update state to show verification completed
        isVerificationCompleted.value = true;
        errorMessage.value = '';

        Get.snackbar(
          'Mobile Verified',
          'Your mobile number has been successfully verified',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );

        // Note: Don't navigate back here, let the UI handle the completion state
      },
    );
  }

  /// Update verification code
  void updateVerificationCode(String code) {
    verificationCode.value = code;
    if (code.length == 6) {
      errorMessage.value = '';
    }
  }

  /// Reset verification states
  void resetVerificationStates() {
    mobileVerificationStartState.value = const ViewModel<bool>.content(false);
    mobileVerificationConfirmState.value = const ViewModel<bool>.content(false);
    errorMessage.value = '';
    verificationCode.value = '';
  }

  /// Clear form
  void clearForm() {
    mobileController.clear();
    confirmMobileController.clear();
    resetVerificationStates();
  }

  /// Check if verification can be started
  bool get canStartVerification => isMobileValid.value && areMobilesMatching.value;

  /// Check if verification can be confirmed
  bool get canConfirmVerification => verificationCode.value.length == 6;

  /// Get loading states for UI
  bool get isStartingVerification => mobileVerificationStartState.value.state == ViewState.loading;
  bool get isConfirmingVerification => mobileVerificationConfirmState.value.state == ViewState.loading;

  // TODO: Replace these simulation methods with actual API calls when endpoints are available
  
  /// Simulate mobile verification start API call
  Future<Result<bool>> _simulateMobileVerificationStart(String mobile) async {
    return ResultHelper.tryCallAsync(
      () async {
        // Simulate network delay
        await Future.delayed(const Duration(seconds: 1));
        
        // Simulate success
        return true;
      },
      errorMessage: 'Unable to send verification code. Please try again.',
      errorCode: 'MOBILE_VERIFICATION_START_ERROR',
      metadata: {
        'mobile': mobile,
        'operation': 'mobileVerificationStart',
      },
    );
  }

  /// Simulate mobile verification confirm API call
  Future<Result<bool>> _simulateMobileVerificationConfirm(String mobile, String code) async {
    return ResultHelper.tryCallAsync(
      () async {
        // Simulate network delay
        await Future.delayed(const Duration(seconds: 1));
        
        // Simulate validation (accept any 6-digit code for demo)
        if (code.length == 6) {
          return true;
        } else {
          throw Exception('Invalid verification code');
        }
      },
      errorMessage: 'Invalid verification code. Please try again.',
      errorCode: 'MOBILE_VERIFICATION_CONFIRM_ERROR',
      metadata: {
        'mobile': mobile,
        'operation': 'mobileVerificationConfirm',
      },
    );
  }
}

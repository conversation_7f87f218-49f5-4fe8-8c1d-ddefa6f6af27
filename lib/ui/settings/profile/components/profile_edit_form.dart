import 'package:darve/ui/settings/profile/profile_edit_controller.dart';
import 'package:darve/ui/settings/profile/components/profile_image_editor.dart';
import 'package:darve/ui/settings/profile/components/profile_field_editor.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileEditForm extends GetView<ProfileEditController> {
  const ProfileEditForm({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: controller.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile Image Section
            const ProfileImageEditor(),
            
            const SizedBox(height: 32),
            
            // Form Fields Section
            const Text(
              'Profile Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Full Name Field
            Obx(() {
              return ProfileFieldEditor(
                label: 'Full Name',
                controller: controller.fullNameController,
                hintText: 'Enter your full name',
                errorText: controller.getFieldError('fullName'),
                onChanged: (value) => controller.updateField('fullName', value),
                required: true,
              );
            }),
            
            const SizedBox(height: 16),
            
            // Username Field
            Obx(() {
              return ProfileFieldEditor(
                label: 'Username',
                controller: controller.usernameController,
                hintText: 'Enter your username',
                errorText: controller.getFieldError('username'),
                onChanged: (value) => controller.updateField('username', value),
                required: true,
                prefixText: '@',
              );
            }),
            
            const SizedBox(height: 16),
            
            // Bio Field
            Obx(() {
              return ProfileFieldEditor(
                label: 'Bio',
                controller: controller.bioController,
                hintText: 'Tell us about yourself',
                errorText: controller.getFieldError('bio'),
                onChanged: (value) => controller.updateField('bio', value),
                maxLines: 3,
                maxLength: 500,
              );
            }),
            
            const SizedBox(height: 24),
            
            // Contact Information Section
            const Text(
              'Contact Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Email Field (with verification)
            Obx(() {
              return ProfileFieldEditor(
                label: 'Email',
                controller: controller.emailController,
                hintText: 'Enter your email address',
                errorText: controller.getFieldError('email'),
                onChanged: (value) => controller.updateField('email', value),
                required: true,
                keyboardType: TextInputType.emailAddress,
                suffixIcon: IconButton(
                  icon: const Icon(Icons.verified_user, color: Styles.primaryColor),
                  onPressed: controller.editEmail,
                  tooltip: 'Verify Email',
                ),
              );
            }),
            
            const SizedBox(height: 16),
            
            // Mobile Number Field (with verification)
            Obx(() {
              return ProfileFieldEditor(
                label: 'Mobile Number',
                controller: controller.mobileController,
                hintText: 'Enter your mobile number',
                errorText: controller.getFieldError('mobileNumber'),
                onChanged: (value) => controller.updateField('mobileNumber', value),
                keyboardType: TextInputType.phone,
                suffixIcon: IconButton(
                  icon: const Icon(Icons.phone_android, color: Styles.primaryColor),
                  onPressed: controller.editMobile,
                  tooltip: 'Verify Mobile',
                ),
              );
            }),
            
            const SizedBox(height: 16),
            
            // Date of Birth Field
            Obx(() {
              return ProfileFieldEditor(
                label: 'Date of Birth',
                controller: controller.dateOfBirthController,
                hintText: 'Select your date of birth',
                errorText: controller.getFieldError('dateOfBirth'),
                onChanged: (value) => controller.updateField('dateOfBirth', value),
                readOnly: true,
                onTap: controller.selectDateOfBirth,
                suffixIcon: const Icon(Icons.calendar_today, color: Styles.primaryColor),
              );
            }),
            
            const SizedBox(height: 32),
            
            // Save Button (for mobile - desktop has it in app bar)
            if (MediaQuery.of(context).size.width < 600)
              SizedBox(
                width: double.infinity,
                height: 50,
                child: Obx(() {
                  return ElevatedButton(
                    onPressed: controller.hasChanges && !controller.isSaving
                        ? controller.saveProfile
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: controller.hasChanges
                          ? Styles.primaryColor
                          : Colors.grey[300],
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                    child: controller.isSaving
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            'Save Changes',
                            style: TextStyle(
                              color: controller.hasChanges ? Colors.white : Colors.grey[600],
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  );
                }),
              ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}

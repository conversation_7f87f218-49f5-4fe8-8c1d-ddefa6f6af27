import 'package:darve/ui/settings/profile/mobile_verification_controller.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';

class MobileVerificationPage extends GetView<MobileVerificationController> {
  const MobileVerificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Verify Mobile Number',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Obx(() {
          if (controller.isVerificationStarted) {
            return _buildVerificationCodeStep();
          } else {
            return _buildMobileInputStep();
          }
        }),
      ),
    );
  }

  Widget _buildMobileInputStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Enter Mobile Number',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        
        const SizedBox(height: 8),
        
        const Text(
          'We\'ll send you a verification code to confirm your mobile number.',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
        
        const SizedBox(height: 32),
        
        // Mobile Number Input
        Obx(() {
          return TextField(
            controller: controller.mobileController,
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              labelText: 'Mobile Number',
              hintText: '****** 567 8900',
              errorText: controller.errorMessage.value.isEmpty 
                  ? null 
                  : controller.errorMessage.value,
              filled: true,
              fillColor: const Color(0xFFF5F5F5),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: Styles.primaryColor,
                  width: 2,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          );
        }),
        
        const SizedBox(height: 16),
        
        // Confirm Mobile Number Input
        Obx(() {
          return TextField(
            controller: controller.confirmMobileController,
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              labelText: 'Confirm Mobile Number',
              hintText: 'Re-enter your mobile number',
              filled: true,
              fillColor: const Color(0xFFF5F5F5),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: Styles.primaryColor,
                  width: 2,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              suffixIcon: controller.areMobilesMatching.value
                  ? const Icon(Icons.check_circle, color: Colors.green)
                  : null,
            ),
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          );
        }),
        
        const Spacer(),
        
        // Send Verification Button
        SizedBox(
          width: double.infinity,
          height: 50,
          child: Obx(() {
            return ElevatedButton(
              onPressed: controller.canStartVerification && !controller.isStartingVerification
                  ? controller.startMobileVerification
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: controller.canStartVerification
                    ? Styles.primaryColor
                    : Colors.grey[300],
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: controller.isStartingVerification
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      'Send Verification Code',
                      style: TextStyle(
                        color: controller.canStartVerification ? Colors.white : Colors.grey[600],
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildVerificationCodeStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Enter Verification Code',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Obx(() {
          return Text(
            'We\'ve sent a 6-digit code to ${controller.mobileController.text}',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          );
        }),
        
        const SizedBox(height: 32),
        
        // PIN Input
        Center(
          child: Pinput(
            length: 6,
            onChanged: controller.updateVerificationCode,
            defaultPinTheme: PinTheme(
              width: 50,
              height: 50,
              textStyle: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.transparent),
              ),
            ),
            focusedPinTheme: PinTheme(
              width: 50,
              height: 50,
              textStyle: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Styles.primaryColor, width: 2),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Error Message
        Obx(() {
          if (controller.errorMessage.value.isNotEmpty) {
            return Center(
              child: Text(
                controller.errorMessage.value,
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            );
          }
          return const SizedBox.shrink();
        }),
        
        const SizedBox(height: 24),
        
        // Resend Code Button
        Center(
          child: TextButton(
            onPressed: controller.startMobileVerification,
            child: const Text(
              'Resend Code',
              style: TextStyle(
                color: Styles.primaryColor,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        
        const Spacer(),
        
        // Verify Button
        SizedBox(
          width: double.infinity,
          height: 50,
          child: Obx(() {
            return ElevatedButton(
              onPressed: controller.canConfirmVerification && !controller.isConfirmingVerification
                  ? controller.confirmMobileVerification
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: controller.canConfirmVerification
                    ? Styles.primaryColor
                    : Colors.grey[300],
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: controller.isConfirmingVerification
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      'Verify Mobile Number',
                      style: TextStyle(
                        color: controller.canConfirmVerification ? Colors.white : Colors.grey[600],
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            );
          }),
        ),
      ],
    );
  }
}

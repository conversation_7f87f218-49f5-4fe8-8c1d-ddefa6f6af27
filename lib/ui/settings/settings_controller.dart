import 'package:darve/ui/settings/data/settings_menu_data.dart';
import 'package:darve/ui/settings/models/settings_menu_model.dart';
import 'package:darve/utils/request_cache.dart';
import 'package:darve/services/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SettingsController extends GetxController {
  // Search state
  final RxString searchQuery = ''.obs;
  final RxBool isSearching = false.obs;
  
  // Auth service
  late final AuthService authService;
  
  @override
  void onInit() {
    super.onInit();
    authService = AuthProvider.auth;
  }

  /// All settings menu items with search metadata
  List<SettingsMenuItem> get allMenuItems => SettingsMenuData.allMenuItems;

  /// Get filtered menu items based on search query
  List<SettingsMenuItem> get filteredMenuItems {
    return SettingsMenuData.getFilteredMenuItems(searchQuery.value);
  }

  /// Get main menu items (for default display)
  List<SettingsMenuItem> get mainMenuItems {
    return SettingsMenuData.mainMenuItems;
  }

  /// Update search query
  void updateSearchQuery(String query) {
    searchQuery.value = query;
    isSearching.value = query.isNotEmpty;
  }

  /// Clear search
  void clearSearch() {
    searchQuery.value = '';
    isSearching.value = false;
  }

  /// Show logout modal
  Future<void> showLogoutModal() async {
    await Get.dialog(
      AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        contentPadding: const EdgeInsets.all(36),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Log out of your account?',
              style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                  color: Colors.black),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 34),
            GestureDetector(
              onTap: () async {
                // Use new AuthController logout method
                await authService.logout();

                // Clear legacy user store and cache
                await RequestCache.getInstance()
                    .then((value) => value.clearCache());
              },
              child: const Text(
                'Log out',
                style: TextStyle(
                    color: Colors.red,
                    fontSize: 18,
                    fontWeight: FontWeight.w500),
              ),
            ),
            const Divider(
              color: Colors.grey,
              height: 20,
              thickness: 0.4,
            ),
            GestureDetector(
              onTap: () => Get.back(),
              child: const Text(
                'Cancel',
                style: TextStyle(
                    color: Colors.black,
                    fontSize: 18,
                    fontWeight: FontWeight.w400),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

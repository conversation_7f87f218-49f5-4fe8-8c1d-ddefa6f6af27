import 'package:darve/ui/components/common/search_field.dart';
import 'package:darve/api/models/user_model.dart';
import 'package:darve/ui/settings/settings_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../utils/styles.dart';
import '../components/settings/profile_card.dart';
import '../components/settings/settings_menu_item.dart';
import '../components/settings/search_results_widget.dart';

class SettingsPage extends GetView<SettingsController> {
  final VoidCallback? goBackToReelsCb;
  const SettingsPage({this.goBackToReelsCb, super.key});

  UserModel? get user => controller.authService.user;



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Settings',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
      ),
      body: Column(
        children: [
          SearchField(
            onChanged: controller.updateSearchQuery,
          ),
          Obx(() {
            if (controller.isSearching.value) {
              return SearchResultsWidget(
                searchResults: controller.filteredMenuItems,
                searchQuery: controller.searchQuery.value,
                onClearSearch: controller.clearSearch,
              );
            } else {
              return _buildDefaultContent();
            }
          }),
        ],
      ),
    );
  }

  Widget _buildDefaultContent() {
    return Expanded(
      child: Column(
        children: [
          ProfileCard(
            avatarUrl: user?.imageUri?.toString() ?? '',
            name: user?.name ?? '',
            username: user?.username ?? '',
          ),
          const SizedBox(height: 4),
          ...controller.mainMenuItems.map((item) => SettingsMenuItem(
            assetPath: item.assetPath,
            text: item.title,
            onTap: item.onTap,
          )),
          const Spacer(),
          Center(
            child: SizedBox(
              width: Get.width * 0.35,
              height: 50,
              child: ElevatedButton(
                onPressed: controller.showLogoutModal,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Styles.primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
                child: Row(
                  children: [
                    Image.asset(
                      'assets/images/settings/logout.png',
                      color: Colors.white,
                      height: 24,
                    ),
                    const Spacer(),
                    const Text(
                      'Logout',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const Spacer(),
          const Text(
            "Darve Mobile 1.0.1",
            style: TextStyle(fontSize: 12, color: Styles.textLightColor),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}

import 'package:darve/routes/route_helper.dart';
import 'package:darve/ui/settings/models/settings_menu_model.dart';
import 'package:darve/ui/settings/privacy/authentication/two_factor_auth_page.dart';
import 'package:darve/ui/settings/privacy/authentication/saved_login_page.dart';
import 'package:darve/ui/settings/privacy/security/logged_devices_page.dart';
import 'package:darve/ui/settings/privacy/security/login_alert_page.dart';
import 'package:darve/ui/settings/shared/web_view_page.dart';
import 'package:darve/utils/constants.dart';
import 'package:get/get.dart';

class SettingsMenuData {
  static const String privacyCategory = 'Privacy and Security';
  static const String contactCategory = 'Contact Us/Support';
  static const String infoCategory = 'More Info';

  /// All settings menu items with search metadata
  static List<SettingsMenuItem> get allMenuItems => [
    // Main menu items
    SettingsMenuItem(
      id: 'privacy_security',
      title: 'Privacy and Security',
      description: 'Manage your account security, passwords, and privacy settings',
      keywords: ['privacy', 'security', 'password', 'authentication', 'login', 'devices'],
      assetPath: 'assets/images/settings/privacy.png',
      onTap: () => RouteHelper.goToPrivacySettings(),
      isMainItem: true,
    ),
    SettingsMenuItem(
      id: 'contact_support',
      title: 'Contact Us/Support',
      description: 'Get help, contact support, and find frequently asked questions',
      keywords: ['contact', 'support', 'help', 'faq', 'about', 'questions'],
      assetPath: 'assets/images/settings/contact.png',
      onTap: () => RouteHelper.goToContactSupport(),
      isMainItem: true,
    ),
    SettingsMenuItem(
      id: 'more_info',
      title: 'More Info',
      description: 'Legal information, terms, policies, and safety guidelines',
      keywords: ['info', 'legal', 'terms', 'policy', 'conditions', 'safety'],
      assetPath: 'assets/images/settings/info.png',
      onTap: () => RouteHelper.goToMoreInfo(),
      isMainItem: true,
    ),
    SettingsMenuItem(
      id: 'edit_profile',
      title: 'Edit Profile',
      description: 'Update your profile information, photo, and personal details',
      keywords: ['edit', 'profile', 'update', 'photo', 'personal', 'information', 'details'],
      assetPath: 'assets/images/settings/profile.png',
      onTap: () => RouteHelper.goToEditProfile(),
      isMainItem: true,
    ),
    
    // Privacy and Security submenu items
    SettingsMenuItem(
      id: 'change_password',
      title: 'Change Password',
      description: 'Update your account password for better security',
      keywords: ['password', 'change', 'update', 'security'],
      onTap: () => RouteHelper.goToChangePassword(),
      parentCategory: privacyCategory,
    ),
    SettingsMenuItem(
      id: 'two_factor_auth',
      title: 'Two Factor Authentication',
      description: 'Add an extra layer of security to your account',
      keywords: ['2fa', 'two factor', 'authentication', 'security', 'verification'],
      onTap: () => Get.to(() => const TwoFactorAuthPage()),
      parentCategory: privacyCategory,
    ),
    SettingsMenuItem(
      id: 'saved_login',
      title: 'Saved Login',
      description: 'Manage your saved login information and credentials',
      keywords: ['saved', 'login', 'credentials', 'remember'],
      onTap: () => Get.to(() => const SavedLoginPage()),
      parentCategory: privacyCategory,
    ),
    SettingsMenuItem(
      id: 'logged_devices',
      title: 'Logged Devices',
      description: 'View and manage devices that have access to your account',
      keywords: ['devices', 'logged', 'sessions', 'active'],
      onTap: () => Get.to(() => const LoggedDevicesPage()),
      parentCategory: privacyCategory,
    ),
    SettingsMenuItem(
      id: 'login_alert',
      title: 'Login Alert',
      description: 'Get notified when someone logs into your account',
      keywords: ['login', 'alert', 'notification', 'security'],
      onTap: () => Get.to(() => const LoginAlertPage()),
      parentCategory: privacyCategory,
    ),
    
    // Contact Us/Support submenu items
    SettingsMenuItem(
      id: 'about',
      title: 'About',
      description: 'Learn more about Darve and our mission',
      keywords: ['about', 'company', 'mission', 'info'],
      onTap: () => Get.to(() => const WebViewPage(
        url: SettingsUrls.aboutUrl,
        title: 'About',
      )),
      parentCategory: contactCategory,
    ),
    SettingsMenuItem(
      id: 'faq',
      title: 'Frequently Asked Questions',
      description: 'Find answers to common questions and issues',
      keywords: ['faq', 'questions', 'help', 'answers', 'common', 'issues'],
      onTap: () => Get.to(() => const WebViewPage(
        url: SettingsUrls.faqUrl,
        title: 'FAQ',
      )),
      parentCategory: contactCategory,
    ),
    SettingsMenuItem(
      id: 'support_24_7',
      title: '24/7 Support',
      description: 'Get round-the-clock support for urgent issues',
      keywords: ['support', '24/7', 'help', 'urgent', 'assistance'],
      onTap: () => Get.to(() => const WebViewPage(
        url: SettingsUrls.supportUrl,
        title: '24/7 Support',
      )),
      parentCategory: contactCategory,
    ),
    SettingsMenuItem(
      id: 'contact',
      title: 'Contact',
      description: 'Get in touch with our team directly',
      keywords: ['contact', 'reach', 'team', 'direct'],
      onTap: () => Get.to(() => const WebViewPage(
        url: SettingsUrls.contactUrl,
        title: 'Contact',
      )),
      parentCategory: contactCategory,
    ),
    
    // More Info submenu items
    SettingsMenuItem(
      id: 'privacy_policy',
      title: 'Privacy Policy',
      description: 'Read our privacy policy and data handling practices',
      keywords: ['privacy', 'policy', 'data', 'handling', 'legal'],
      onTap: () => Get.to(() => const WebViewPage(
        url: SettingsUrls.privacyPolicyUrl,
        title: 'Privacy Policy',
      )),
      parentCategory: infoCategory,
    ),
    SettingsMenuItem(
      id: 'terms_of_service',
      title: 'Terms of Service',
      description: 'Review the terms and conditions for using Darve',
      keywords: ['terms', 'service', 'conditions', 'legal', 'agreement'],
      onTap: () => Get.to(() => const WebViewPage(
        url: SettingsUrls.termsOfServiceUrl,
        title: 'Terms of Service',
      )),
      parentCategory: infoCategory,
    ),
    SettingsMenuItem(
      id: 'conditions',
      title: 'Conditions',
      description: 'Additional terms and conditions',
      keywords: ['conditions', 'terms', 'additional', 'legal'],
      onTap: () => Get.to(() => const WebViewPage(
        url: SettingsUrls.conditionsUrl,
        title: 'Conditions',
      )),
      parentCategory: infoCategory,
    ),
    SettingsMenuItem(
      id: 'safety_centre',
      title: 'Safety Centre',
      description: 'Learn about safety features and community guidelines',
      keywords: ['safety', 'centre', 'guidelines', 'community', 'features'],
      onTap: () => Get.to(() => const WebViewPage(
        url: SettingsUrls.safetyCentreUrl,
        title: 'Safety Centre',
      )),
      parentCategory: infoCategory,
    ),
  ];

  /// Get main menu items (for default display)
  static List<SettingsMenuItem> get mainMenuItems {
    return allMenuItems.where((item) => item.isMainItem).toList();
  }

  /// Get filtered menu items based on search query
  static List<SettingsMenuItem> getFilteredMenuItems(String query) {
    if (query.isEmpty) {
      return [];
    }
    
    return allMenuItems
        .where((item) => item.matchesSearch(query))
        .toList();
  }
}

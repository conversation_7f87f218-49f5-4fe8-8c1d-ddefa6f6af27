import 'package:flutter/material.dart';

class SettingsMenuItem {
  final String id;
  final String title;
  final String? description;
  final List<String> keywords;
  final String? assetPath;
  final VoidCallback onTap;
  final String? parentCategory;
  final bool isMainItem;

  const SettingsMenuItem({
    required this.id,
    required this.title,
    this.description,
    required this.keywords,
    this.assetPath,
    required this.onTap,
    this.parentCategory,
    this.isMainItem = false,
  });

  /// Check if this menu item matches the search query
  bool matchesSearch(String query) {
    if (query.isEmpty) return true;
    
    final searchQuery = query.toLowerCase();
    
    // Search in title
    if (title.toLowerCase().contains(searchQuery)) return true;
    
    // Search in description
    if (description?.toLowerCase().contains(searchQuery) == true) return true;
    
    // Search in keywords
    for (final keyword in keywords) {
      if (keyword.toLowerCase().contains(searchQuery)) return true;
    }
    
    // Search in parent category
    if (parentCategory?.toLowerCase().contains(searchQuery) == true) return true;
    
    return false;
  }

  /// Get display text for search results with context
  String get displayText {
    if (parentCategory != null && !isMainItem) {
      return '$parentCategory > $title';
    }
    return title;
  }
}

class SettingsMenuData {
  static const String privacyCategory = 'Privacy and Security';
  static const String contactCategory = 'Contact Us/Support';
  static const String infoCategory = 'More Info';
}

import 'package:darve/ui/settings/delete_account/verify_delete_account_controller.dart';
import 'package:get/get.dart';

/// Binding for Verify Delete Account functionality
/// Handles dependency injection for the delete account flow
class VerifyDeleteAccountBinding extends Bindings {
  @override
  void dependencies() {
    // Register VerifyDeleteAccountController
    Get.lazyPut<VerifyDeleteAccountController>(
      () => VerifyDeleteAccountController(),
      fenix: true, // Keep alive during the session
    );
  }
}

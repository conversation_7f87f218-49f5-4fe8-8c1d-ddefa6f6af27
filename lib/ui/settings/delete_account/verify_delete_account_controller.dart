import 'dart:async';
import 'package:darve/api/repositories/user_repository.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/ui/core/entity_state_widget.dart';
import 'package:darve/utils/errors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Controller for Verify Delete Account functionality
/// Handles verification code sending, validation, and account deletion
class VerifyDeleteAccountController extends GetxController {
  // Dependencies
  final ErrorsHandle _errorHandler = ErrorsHandle();
  late final UserRepository _userRepository;

  // Reactive state variables
  final RxString verificationCode = ''.obs;
  final RxBool canResendCode = true.obs;
  final RxInt resendCountdown = 0.obs;
  final RxBool isDeleting = false.obs;
  
  // State management
  final Rx<ViewModel<bool>> deleteAccountState = const ViewModel<bool>.empty().obs;
  final Rx<ViewModel<bool>> sendCodeState = const ViewModel<bool>.empty().obs;

  // Timer for resend countdown
  Timer? _resendTimer;

  @override
  void onInit() {
    super.onInit();
    _userRepository = ServiceProvider.userRepository;
    
    // Automatically send verification code when page loads
    sendVerificationCode();
  }

  @override
  void onClose() {
    _resendTimer?.cancel();
    super.onClose();
  }

  /// Update verification code
  void updateVerificationCode(String code) {
    verificationCode.value = code;
  }

  /// Check if deletion can be confirmed
  bool get canConfirmDeletion => verificationCode.value.length == 6;

  /// Send verification code to user's email
  Future<void> sendVerificationCode() async {
    try {
      sendCodeState.value = const ViewModel<bool>.loading();
      
      // TODO: Replace with actual API call
      // final result = await _userRepository.sendDeleteAccountVerificationCode();
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Simulate success
      sendCodeState.value = const ViewModel<bool>.content(true);
      
      Get.snackbar(
        'Verification Code Sent',
        'A 6-digit verification code has been sent to your email',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
      
      _startResendCountdown();
      
    } catch (error) {
      sendCodeState.value = ViewModel<bool>.error(error.toString());
      _errorHandler.displayErrorToast(error.toString(), 'sendVerificationCode');
    }
  }

  /// Resend verification code
  Future<void> resendCode() async {
    if (!canResendCode.value) return;
    
    await sendVerificationCode();
  }

  /// Confirm account deletion with verification code
  Future<void> confirmDeletion() async {
    if (!canConfirmDeletion || isDeleting.value) return;
    
    try {
      isDeleting.value = true;
      deleteAccountState.value = const ViewModel<bool>.loading();
      
      // TODO: Replace with actual API call
      // final result = await _userRepository.deleteAccount(verificationCode.value);
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Simulate success
      deleteAccountState.value = const ViewModel<bool>.content(true);
      
      // Show success message
      Get.snackbar(
        'Account Deleted',
        'Your account has been successfully deleted',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
      
      // Navigate to login/welcome screen after deletion
      await Future.delayed(const Duration(seconds: 1));
      Get.offAllNamed('/login'); // Replace with your login route
      
    } catch (error) {
      deleteAccountState.value = ViewModel<bool>.error(error.toString());
      _errorHandler.displayErrorToast(error.toString(), 'confirmDeletion');
      
      // Show specific error for invalid code
      if (error.toString().contains('invalid') || error.toString().contains('code')) {
        Get.snackbar(
          'Invalid Code',
          'The verification code you entered is incorrect. Please try again.',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );
        
        // Clear the code to allow retry
        verificationCode.value = '';
      }
    } finally {
      isDeleting.value = false;
    }
  }

  /// Start countdown timer for resend code
  void _startResendCountdown() {
    canResendCode.value = false;
    resendCountdown.value = 60; // 60 seconds countdown
    
    _resendTimer?.cancel();
    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (resendCountdown.value > 0) {
        resendCountdown.value--;
      } else {
        canResendCode.value = true;
        timer.cancel();
      }
    });
  }

  /// Show final confirmation dialog before deletion
  void showFinalConfirmation() {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Text(
          'Final Confirmation',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.red,
          ),
        ),
        content: const Text(
          'This is your last chance to cancel. Once confirmed, your account and all data will be permanently deleted and cannot be recovered.',
          style: TextStyle(
            fontSize: 16,
            color: Colors.black87,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text(
              'Cancel',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back(); // Close dialog
              confirmDeletion(); // Proceed with deletion
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Delete Forever',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

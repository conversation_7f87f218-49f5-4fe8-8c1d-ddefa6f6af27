import 'package:darve/ui/settings/delete_account/verify_delete_account_controller.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';

/// Verify Delete Account Page
/// Matches the exact design from the provided image
/// Features: Blue header with logo, white content area, 6-digit PIN input
class VerifyDeleteAccountPage extends GetView<VerifyDeleteAccountController> {
  const VerifyDeleteAccountPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Styles.primaryColor, // Blue background
      body: SafeArea(
        child: Column(
          children: [
            // Blue Header Section
            _buildHeader(context),
            
            // White Content Section
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                ),
                child: _buildContent(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      child: Column(
        children: [
          // Back button and logo row
          Row(
            children: [
              GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: const Icon(
                  Icons.arrow_back_ios,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const Spacer(),
            ],
          ),
          
          const SizedBox(height: 40),
          
          // App Logo - matching the design
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                'dv',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Styles.primaryColor,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 60),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          const SizedBox(height: 40),
          
          // Title
          const Text(
            'Verify to Delete Account',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Description
          const Text(
            'We have sent a 6-digit code to your email.\nEnter it to confirm deletion.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
              height: 1.5,
            ),
          ),
          
          const SizedBox(height: 48),
          
          // PIN Input - 6 digits matching the design
          _buildPinInput(),
          
          const SizedBox(height: 32),
          
          // Resend code button
          Obx(() {
            return TextButton(
              onPressed: controller.canResendCode.value 
                  ? controller.resendCode 
                  : null,
              child: Text(
                controller.canResendCode.value 
                    ? 'Resend code'
                    : 'Resend code in ${controller.resendCountdown.value}s',
                style: TextStyle(
                  color: controller.canResendCode.value 
                      ? Styles.primaryColor 
                      : Colors.grey,
                  fontSize: 16,
                ),
              ),
            );
          }),
          
          const Spacer(),
          
          // Confirm Button
          Obx(() {
            return Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 40),
              child: ElevatedButton(
                onPressed: controller.canConfirmDeletion && !controller.isDeleting.value
                    ? controller.confirmDeletion
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: controller.canConfirmDeletion
                      ? Styles.primaryColor
                      : Colors.grey[300],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: controller.isDeleting.value
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Confirm',
                        style: TextStyle(
                          color: controller.canConfirmDeletion
                              ? Colors.white
                              : Colors.grey[600],
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildPinInput() {
    return Pinput(
      length: 6,
      onChanged: (value) {
        controller.updateVerificationCode(value);
      },
      onCompleted: (pin) {
        // Auto-submit when all 6 digits are entered
        if (controller.canConfirmDeletion) {
          controller.confirmDeletion();
        }
      },
      defaultPinTheme: PinTheme(
        width: 56,
        height: 64,
        textStyle: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: Colors.black,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: const Color(0xFFF5F5F5), // Light grey background matching the image
          border: Border.all(
            color: Colors.transparent,
            width: 1,
          ),
        ),
      ),
      focusedPinTheme: PinTheme(
        width: 56,
        height: 64,
        textStyle: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: Colors.black,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: const Color(0xFFF5F5F5),
          border: Border.all(
            color: Styles.primaryColor,
            width: 2,
          ),
        ),
      ),
      submittedPinTheme: PinTheme(
        width: 56,
        height: 64,
        textStyle: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: Colors.black,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: const Color(0xFFF5F5F5),
          border: Border.all(
            color: Colors.grey[300]!,
            width: 1,
          ),
        ),
      ),
      errorPinTheme: PinTheme(
        width: 56,
        height: 64,
        textStyle: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: Colors.red,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: const Color(0xFFF5F5F5),
          border: Border.all(
            color: Colors.red,
            width: 2,
          ),
        ),
      ),
      keyboardType: TextInputType.number,
    );
  }
}

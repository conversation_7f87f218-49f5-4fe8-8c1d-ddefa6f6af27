import 'package:darve/api/repositories/user_repository.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/ui/core/entity_state_widget.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/result.dart';
import 'package:darve/utils/validators.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PrivacySettingsController extends GetxController {
  // Form controllers
  final TextEditingController currentPasswordController = TextEditingController();
  final TextEditingController newPasswordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();

  // Form key for validation
  final formKey = GlobalKey<FormState>();

  // State management using ViewModel pattern
  final Rx<ViewModel<bool>> changePasswordState = const ViewModel<bool>.content(false).obs;

  // UI state
  final RxBool isCurrentPasswordVisible = false.obs;
  final RxBool isNewPasswordVisible = false.obs;
  final RxBool isConfirmPasswordVisible = false.obs;

  // Error handling
  final ErrorsHandle _errorHandler = ErrorsHandle();
  late final UserRepository _userRepository;

  @override
  void onInit() {
    super.onInit();
    _userRepository = ServiceProvider.userRepository;
  }

  @override
  void onClose() {
    currentPasswordController.dispose();
    newPasswordController.dispose();
    confirmPasswordController.dispose();
    super.onClose();
  }

  /// Toggle password visibility for current password field
  void toggleCurrentPasswordVisibility() {
    isCurrentPasswordVisible.value = !isCurrentPasswordVisible.value;
  }

  /// Toggle password visibility for new password field
  void toggleNewPasswordVisibility() {
    isNewPasswordVisible.value = !isNewPasswordVisible.value;
  }

  /// Toggle password visibility for confirm password field
  void toggleConfirmPasswordVisibility() {
    isConfirmPasswordVisible.value = !isConfirmPasswordVisible.value;
  }

  /// Validates the current password field
  String? validateCurrentPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Current password is required';
    }
    return null;
  }

  /// Validates the new password field
  String? validateNewPassword(String? value) {
    return Validators.validatePassword(value);
  }

  /// Validates the confirm password field
  String? validateConfirmPassword(String? value) {
    return Validators.validatePasswordConfirmation(value, newPasswordController.text);
  }

  /// Change password functionality
  Future<void> changePassword() async {
    // Validate form first
    if (!formKey.currentState!.validate()) {
      return;
    }

    // Set loading state
    changePasswordState.value = const ViewModel<bool>.loading();

    // Call API to change password
    final result = await _userRepository.changePassword(
      currentPasswordController.text.trim(),
      newPasswordController.text.trim(),
    );

    // Use ResultExtensions toViewModel pattern for consistency
    changePasswordState.value = result.toViewModel(
      onError: (error) => _errorHandler.displayErrorToast(error, 'changePassword'),
      onSuccess: (_) {
        // Clear form fields
        _clearForm();

        // Show success message
        Get.snackbar(
          'Success',
          'Password changed successfully',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.TOP,
        );

        // Navigate back after a short delay
        Future.delayed(const Duration(seconds: 1), () {
          Get.back();
        });
      },
    );
  }

  /// Clear all form fields
  void _clearForm() {
    currentPasswordController.clear();
    newPasswordController.clear();
    confirmPasswordController.clear();
  }



  /// Reset the change password state (for retry functionality)
  void resetChangePasswordState() {
    changePasswordState.value = const ViewModel<bool>.content(false);
  }

  /// Check if form is valid for enabling/disabling submit button
  bool get isFormValid {
    return currentPasswordController.text.isNotEmpty &&
           newPasswordController.text.isNotEmpty &&
           confirmPasswordController.text.isNotEmpty &&
           newPasswordController.text == confirmPasswordController.text;
  }

  /// Get loading state for UI
  bool get isLoading => changePasswordState.value.state == ViewState.loading;
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SearchPageBinding extends Bindings {
  @override
  void dependencies() {
    // Register SearchController for search functionality
    Get.lazyPut<SearchController>(() => SearchController());
    
    // Note: ProfileController will be registered dynamically when user taps on search result
    // This is handled in SearchPage when navigating to profile
  }
}

// Enhanced error models for comprehensive error handling
/// Error severity levels for reporting and handling
enum ErrorSeverity {
  /// Expected errors that users can fix (validation, form errors)
  expected,
  /// Unexpected errors that should be reported but app can continue
  warning,
  /// Unexpected errors that affect functionality
  error,
  /// Critical errors that crash the app or make it unusable
  fatal,
}

/// Base error class for the application
abstract class AppError implements Exception {
  final String message;
  final String? code;
  final ErrorSeverity severity;
  final dynamic originalError;
  final String? requestId;
  final Map<String, dynamic>? metadata;

  const AppError({
    required this.message,
    this.code,
    this.severity = ErrorSeverity.error,
    this.originalError,
    this.requestId,
    this.metadata,
  });

  @override
  String toString() => message;

  /// Convert error to a map for reporting
  Map<String, dynamic> toMap() {
    return {
      'message': message,
      'code': code,
      'severity': severity.name,
      'requestId': requestId,
      'metadata': metadata,
      'originalError': originalError?.toString(),
    };
  }
}

/// Generic API errors from server responses
/// Handles any error format from the server with consistent JSON structure
class ApiError extends AppError {
  final Map<String, List<String>> fieldErrors;
  final String? rawErrorMessage;

  const ApiError({
    required super.message,
    this.fieldErrors = const {},
    this.rawErrorMessage,
    super.code,
    super.requestId,
    super.metadata,
    super.severity = ErrorSeverity.expected,
  });

  /// Factory constructor for field-specific errors (like validation)
  factory ApiError.withFields({
    required String message,
    required Map<String, List<String>> fieldErrors,
    String? code,
    String? requestId,
    Map<String, dynamic>? metadata,
    ErrorSeverity severity = ErrorSeverity.expected,
  }) {
    return ApiError(
      message: message,
      fieldErrors: fieldErrors,
      code: code,
      requestId: requestId,
      metadata: metadata,
      severity: severity,
    );
  }

  /// Factory constructor for simple API errors
  factory ApiError.simple({
    required String message,
    String? code,
    String? requestId,
    Map<String, dynamic>? metadata,
    ErrorSeverity severity = ErrorSeverity.expected,
  }) {
    return ApiError(
      message: message,
      rawErrorMessage: message,
      code: code,
      requestId: requestId,
      metadata: metadata,
      severity: severity,
    );
  }

  /// Check if this error has field-specific errors
  bool get hasFieldErrors => fieldErrors.isNotEmpty;

  /// Get user-friendly error message for a specific field
  String? getFieldError(String field) {
    final errors = fieldErrors[field];
    return errors?.isNotEmpty == true ? errors!.first : null;
  }

  /// Get all field errors as a single formatted message
  String get formattedMessage {
    if (fieldErrors.isEmpty) return message;

    final errorMessages = <String>[];
    fieldErrors.forEach((field, errors) {
      if (errors.isNotEmpty) {
        errorMessages.add('$field: ${errors.join(', ')}');
      }
    });

    return errorMessages.join('\n');
  }

  /// Get user-friendly display message
  String get displayMessage {
    if (hasFieldErrors) {
      return formattedMessage;
    }
    return rawErrorMessage ?? message;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map['fieldErrors'] = fieldErrors;
    map['rawErrorMessage'] = rawErrorMessage;
    map['formattedMessage'] = formattedMessage;
    map['hasFieldErrors'] = hasFieldErrors;
    return map;
  }
}

/// Network and HTTP related errors
class NetworkError extends AppError {
  final int? statusCode;
  final String? statusMessage;
  final String? endpoint;
  final String? method;

  const NetworkError({
    required super.message,
    this.statusCode,
    this.statusMessage,
    this.endpoint,
    this.method,
    super.code,
    super.severity = ErrorSeverity.warning,
    super.originalError,
    super.requestId,
    super.metadata,
  });

  factory NetworkError.timeout() => const NetworkError(
        message: 'Connection timeout. Please check your internet connection.',
        code: 'NETWORK_TIMEOUT',
        severity: ErrorSeverity.warning,
      );

  factory NetworkError.noConnection() => const NetworkError(
        message: 'No internet connection.',
        code: 'NO_CONNECTION',
        severity: ErrorSeverity.warning,
      );

  factory NetworkError.serverError({
    int? statusCode,
    String? message,
    String? endpoint,
    String? method,
    String? requestId,
  }) => NetworkError(
        message: message ?? 'Server error. Please try again later.',
        statusCode: statusCode,
        endpoint: endpoint,
        method: method,
        code: 'SERVER_ERROR',
        severity: ErrorSeverity.error,
        requestId: requestId,
      );

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    map['statusCode'] = statusCode;
    map['statusMessage'] = statusMessage;
    map['endpoint'] = endpoint;
    map['method'] = method;
    return map;
  }
}

/// Authentication specific errors
class AuthError extends AppError {
  const AuthError({
    required super.message,
    super.code,
    super.severity = ErrorSeverity.expected,
    super.originalError,
    super.requestId,
    super.metadata,
  });

  // Factory constructors for common auth errors
  factory AuthError.invalidCredentials() => const AuthError(
        message: 'Wrong username or password',
        code: 'INVALID_CREDENTIALS',
      );

  factory AuthError.userNotFound() => const AuthError(
        message: 'Wrong username or password',
        code: 'USER_NOT_FOUND',
      );

  factory AuthError.sessionExpired() => const AuthError(
        message: 'Session Expired! Log in again',
        code: 'SESSION_EXPIRED',
        severity: ErrorSeverity.warning,
      );

  factory AuthError.networkError() => const AuthError(
        message: 'Can not establish connection with Darve backend',
        code: 'NETWORK_ERROR',
        severity: ErrorSeverity.warning,
      );

  factory AuthError.socialLoginFailed(String provider) => AuthError(
        message: 'Failed to sign in with $provider',
        code: 'SOCIAL_LOGIN_FAILED',
      );

  factory AuthError.unknown(dynamic error) => AuthError(
        message: error.toString(),
        code: 'UNKNOWN_ERROR',
        severity: ErrorSeverity.error,
        originalError: error,
      );

  // Additional factory methods for compatibility with existing auth service
  factory AuthError.tokenMissing() => const AuthError(
        message: 'Authentication token is missing',
        code: 'TOKEN_MISSING',
      );

  factory AuthError.registrationFailed(String reason) => AuthError(
        message: 'Registration failed: $reason',
        code: 'REGISTRATION_FAILED',
      );

  factory AuthError.passwordResetFailed(String reason) => AuthError(
        message: 'Password reset failed: $reason',
        code: 'PASSWORD_RESET_FAILED',
      );

  factory AuthError.invalidResetCode() => const AuthError(
        message: 'Invalid or expired reset code',
        code: 'INVALID_RESET_CODE',
      );

  factory AuthError.userNotFoundForReset() => const AuthError(
        message: 'No account found with that email or username',
        code: 'USER_NOT_FOUND_FOR_RESET',
      );

  factory AuthError.emailVerificationFailed(String reason) => AuthError(
        message: 'Email verification failed: $reason',
        code: 'EMAIL_VERIFICATION_FAILED',
      );

  factory AuthError.invalidVerificationCode() => const AuthError(
        message: 'Invalid or expired verification code',
        code: 'INVALID_VERIFICATION_CODE',
      );

  factory AuthError.emailAlreadyVerified() => const AuthError(
        message: 'Email is already verified',
        code: 'EMAIL_ALREADY_VERIFIED',
      );

  factory AuthError.invalidEmailFormat() => const AuthError(
        message: 'Invalid email format',
        code: 'INVALID_EMAIL_FORMAT',
      );

  // Helper method to create AuthError from exception
  factory AuthError.fromException(dynamic exception) {
    final errorString = exception.toString();

    if (errorString.contains("Authentication failed")) {
      return AuthError.invalidCredentials();
    } else if (errorString.contains("username not found")) {
      return AuthError.userNotFound();
    } else if (errorString.contains("The provided JWT")) {
      return AuthError.sessionExpired();
    } else if (errorString.contains("ClientException with SocketException")) {
      return AuthError.networkError();
    } else {
      return AuthError.unknown(exception);
    }
  }
}

/// Application logic errors
class AppLogicError extends AppError {
  const AppLogicError({
    required super.message,
    super.code,
    super.severity = ErrorSeverity.error,
    super.originalError,
    super.requestId,
    super.metadata,
  });

  factory AppLogicError.unexpected(dynamic error, {String? context}) => AppLogicError(
        message: context != null 
            ? 'Unexpected error in $context: ${error.toString()}'
            : 'Unexpected error: ${error.toString()}',
        code: 'UNEXPECTED_ERROR',
        severity: ErrorSeverity.fatal,
        originalError: error,
      );
}

/// User-facing errors that should be displayed to users
class UserError extends AppError {
  const UserError({
    required super.message,
    super.code,
    super.severity = ErrorSeverity.expected,
    super.originalError,
    super.requestId,
    super.metadata,
  });

  factory UserError.contentTooShort() => const UserError(
        message: 'Content length too short, need at least 5 characters',
        code: 'CONTENT_TOO_SHORT',
      );

  factory UserError.alreadyFollowing() => const UserError(
        message: 'Already following the user!',
        code: 'ALREADY_FOLLOWING',
      );

  factory UserError.custom(String message, {String? code}) => UserError(
        message: message,
        code: code ?? 'CUSTOM_ERROR',
      );
}

// Loading states for auth operations
enum AuthLoadingState {
  idle,
  loading,
  success,
  error,
}

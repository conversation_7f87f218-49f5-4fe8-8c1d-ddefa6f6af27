import 'package:darve/api/models/user_model.dart';
import 'package:darve/services/error/error_models.dart';

// Authentication state model
class AuthState {
  final bool isLoggedIn;
  final String? token;
  final UserModel? user;
  final AuthLoadingState loadingState;
  final AuthError? error;

  const AuthState({
    this.isLoggedIn = false,
    this.token,
    this.user,
    this.loadingState = AuthLoadingState.idle,
    this.error,
  });

  // Factory constructor for initial state
  factory AuthState.initial() => const AuthState();

  // Factory constructor for loading state
  factory AuthState.loading() => const AuthState(
        loadingState: AuthLoadingState.loading,
      );

  // Factory constructor for authenticated state
  factory AuthState.authenticated({
    required String token,
    required UserModel user,
  }) =>
      AuthState(
        isLoggedIn: true,
        token: token,
        user: user,
        loadingState: AuthLoadingState.success,
      );

  // Factory constructor for error state
  factory AuthState.error(AuthError error) => AuthState(
        loadingState: AuthLoadingState.error,
        error: error,
      );

  // Factory constructor for unauthenticated state
  factory AuthState.unauthenticated() => const AuthState(
        isLoggedIn: false,
        token: null,
        user: null,
        loadingState: AuthLoadingState.idle,
      );

  // Copy with method for state updates
  AuthState copyWith({
    bool? isLoggedIn,
    String? token,
    UserModel? user,
    AuthLoadingState? loadingState,
    AuthError? error,
  }) {
    return AuthState(
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
      token: token ?? this.token,
      user: user ?? this.user,
      loadingState: loadingState ?? this.loadingState,
      error: error ?? this.error,
    );
  }

  // Clear error state
  AuthState clearError() => AuthState(
        isLoggedIn: isLoggedIn,
        token: token,
        user: user,
        loadingState: AuthLoadingState.idle,
        error: null,
      );

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AuthState &&
          runtimeType == other.runtimeType &&
          isLoggedIn == other.isLoggedIn &&
          token == other.token &&
          user == other.user &&
          loadingState == other.loadingState &&
          error == other.error;

  @override
  int get hashCode =>
      isLoggedIn.hashCode ^
      token.hashCode ^
      user.hashCode ^
      loadingState.hashCode ^
      error.hashCode;

  @override
  String toString() {
    return 'AuthState{isLoggedIn: $isLoggedIn, token: ${token != null ? '[HIDDEN]' : 'null'}, user: $user, loadingState: $loadingState, error: $error}';
  }
}

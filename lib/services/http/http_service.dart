import 'dart:convert';
import 'package:darve/services/error/error_models.dart';
import 'package:dio/dio.dart';
import 'package:darve/config.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import 'interceptors/json_response_interceptor.dart';
import 'interceptors/error_interceptor.dart';

class HttpService {
  late final Dio _dio;
  String? authToken;

  HttpService() {
    _dio = Dio();
    _setupInterceptors();
  }

  void _setupInterceptors() {
    _dio.options.baseUrl = AppConfig.instance.apiUrl;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.sendTimeout = const Duration(seconds: 30);

    // Add JSON response interceptor to handle incorrect Content-Type headers
    _dio.interceptors.add(JsonResponseInterceptor());

    // Add error interceptor for parsing and reporting errors
    _dio.interceptors.add(ErrorInterceptor());

    // Request interceptor for adding auth headers
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add common headers
          options.headers.addAll({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          });

          // Add auth token if available
          final token = authToken;
          if (token != null && token.isNotEmpty) {
            options.headers['Cookie'] = 'jwt=$token';
          }

          handler.next(options);
        },
        onResponse: (response, handler) {
          handler.next(response);
        },
        onError: (error, handler) {
          handler.next(error);
        },
      ),
    );

    // Add comprehensive logging interceptor (only in debug mode)
    if (kDebugMode) {
      // _dio.interceptors.add(CustomLogInterceptor());
      _dio.interceptors.add(LogInterceptor(responseBody: true, responseHeader: false));
    }

  }

  // GET request
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // GET request with stream response
  Stream<String?> getStream(
    String path, {
    Map<String, dynamic>? queryParameters,
  }) async* {
    try {
      final completePath =
          path.startsWith('http') ? path : _dio.options.baseUrl + path;
      final request = http.Request('GET', Uri.parse(completePath));
      request.headers.addAll({
        'Content-Type': 'text/event-stream',
        'Cookie': 'jwt=$authToken',
        'Accept': 'application/json',
      });

      final response = await request.send();
      if (response.statusCode != 200) {
        throw Exception('Failed to connect to SSE: ${response.reasonPhrase}');
      }

      final stream =
          response.stream.transform(utf8.decoder).asBroadcastStream();

      await for (var line in stream) {
        if (line.startsWith('data:')) {
          final jsonData = line.substring(5).trim();
          try {
            // Attempt to decode the JSON data
            yield jsonData;
          } catch (e) {
            // If decoding fails, log the error and send a message back
            debugPrint('Invalid JSON received in SSE data: $jsonData');
            yield null;
          }
        } else {
          // Optionally, yield an error or handle it in a different way
          yield null;
        }
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // POST request
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // PUT request
  Future<Response<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.put<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // PATCH request
  Future<Response<T>> patch<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.patch<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // DELETE request
  Future<Response<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.delete<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Handle Dio errors and convert to app-specific exceptions
  Exception _handleDioError(DioException error) {
    // If the error was already processed by our ErrorInterceptor,
    // it should contain an AppError in the error field
    if (error.error is AppError) {
      return error.error as AppError;
    }

    // Fallback to legacy error handling if ErrorInterceptor didn't process it
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return Exception(
            'Connection timeout. Please check your internet connection.');

      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = error.response?.data?.toString() ?? error.message;

        switch (statusCode) {
          case 401:
            return Exception('Unauthorized. Please log in again.');
          case 403:
            return Exception('Access forbidden.');
          case 404:
            return Exception('Resource not found.');
          case 500:
            return Exception('Server error. Please try again later.');
          default:
            return Exception('Request failed: $message');
        }

      case DioExceptionType.cancel:
        return Exception('Request was cancelled.');

      case DioExceptionType.connectionError:
        return Exception('No internet connection.');

      default:
        return Exception('Network error: ${error.message}');
    }
  }
}

import 'package:flutter/material.dart';

class UserIdHelper {
  static const String APP_GATEWAY_WALLET = "wallet:app_gateway_wallet";

  @Deprecated("use IdExtractor")
  List<String>? getOtherUserIds(List<dynamic> chatRoom, String loggedInUser) {
    List<String> chatRoomUserIds = [];
    for (var c in chatRoom) {
      if ('${c['tb']}:${c['id']['String']}' == loggedInUser) {
        continue;
      }
      chatRoomUserIds.add('${c['tb']}:${c['id']['String']}');
    }
    return chatRoomUserIds;
  }

  @Deprecated("use IdExtractor")
  String getUserId(Map<String, dynamic> user) {
    try {
      final userId = "${user['id']['tb']}:${user['id']['id']['String']}";
      return userId;
    } catch (e) {
      debugPrint("UserIdHelper.getUserId user=$user error===$e");
      return "";
    }
  }
}

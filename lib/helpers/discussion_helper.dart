import 'package:flutter/material.dart';

@Deprecated("use IdExtractor")
class DiscussionIdHelper {
  String getDiscussionId(dynamic discussion, {String? propName}) {
    try {
      if (propName != null && discussion.containsKey(propName)) {
        return "${discussion[propName]['tb']}:${discussion[propName]['id']['String']}";
      }
      return "${discussion['id']['tb']}:${discussion['id']['id']['String']}";
    } catch (e) {
      debugPrint(
          "DiscussionIdHelper.getDiscussionId discussion=$discussion error===$e");
      return "";
    }
  }
}

import 'package:darve/services/http/http_service.dart';
import 'package:dio/dio.dart';
import 'package:darve/utils/constants.dart';

class ChallengeRepository {
  final HttpService _dioService;

  ChallengeRepository(this._dioService);

  // Tasks API endpoints
  Future<dynamic> getReceivedTasks() async {
    final response = await _dioService.get(ApiPaths.getReceivedTasks);
    return response.data;
  }

  Future<dynamic> getGivenTasks() async {
    final response = await _dioService.get(ApiPaths.getGivenTasks);
    return response.data;
  }

  Future<dynamic> acceptTask(String taskId) async {
    final response = await _dioService.post(ApiPaths.acceptTask(taskId));
    return response.data;
  }

  Future<dynamic> rejectTask(String taskId) async {
    final response = await _dioService.post(ApiPaths.rejectTask(taskId));
    return response.data;
  }

  Future<dynamic> deliverTask(String taskId, String filePath) async {
    final formData = FormData.fromMap({
      'file': await MultipartFile.fromFile(filePath),
    });

    final response = await _dioService.post(
      ApiPaths.deliverTask(taskId),
      data: formData,
    );
    return response.data;
  }

  Future<dynamic> upsertDonor(String taskId, Map<String, dynamic> donorData) async {
    final response = await _dioService.post(
      ApiPaths.upsertDonor(taskId),
      data: donorData,
    );
    return response.data;
  }


  // Legacy/Deprecated methods for backward compatibility
  @Deprecated('Use getReceivedTasks instead')
  Future<dynamic> getAllReceivedTaskRequests() async {
    final response = await _dioService.get(ApiPaths.getAllReceivedTaskRequests);
    return response.data;
  }

  @Deprecated('Use getGivenTasks instead')
  Future<dynamic> getGivenChallenges() async {
    final response = await _dioService.get(ApiPaths.getGivenChallenges);
    return response.data;
  }

  @Deprecated('Use acceptTask instead')
  Future<dynamic> acceptChallenge(String taskId, bool accepted) async {
    if (accepted) {
      final response = await _dioService.post(ApiPaths.acceptChallenge(taskId));
      return response.data;
    } else {
      final response = await _dioService.post(ApiPaths.rejectTask(taskId));
      return response.data;
    }
  }

  @Deprecated('Use deliverTask instead')
  Future<dynamic> deliverTaskRequest(String taskId, String filePath, String postId) async {
    final formData = FormData.fromMap({
      'post_id': postId,
      'file': await MultipartFile.fromFile(filePath),
    });

    final response = await _dioService.post(
      ApiPaths.deliverTaskRequest(taskId),
      data: formData,
    );
    return response.data;
  }

  // Additional legacy methods that were removed but might be in use
  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> getTaskRequests(String postId) async {
    final response = await _dioService.get(ApiPaths.getTaskRequests(postId));
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> getReceivedTaskRequestsForPost(String postId) async {
    final response = await _dioService.get(ApiPaths.getReceivedTaskRequestsForPost(postId));
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> createChallenge(Map<String, dynamic> challengeData) async {
    final response = await _dioService.post(ApiPaths.createChallenge, data: challengeData);
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> getMyChallenges() async {
    final response = await _dioService.get(ApiPaths.getMyChallenges);
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> addParticipant(String challengeId, int amount) async {
    final response = await _dioService.post(
      ApiPaths.addParticipant(challengeId),
      data: {'amount': amount},
    );
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> createTaskRequest({
    required String toUserId,
    required String content,
    required String postId,
    required double offerAmount,
  }) async {
    final response = await _dioService.post(
      ApiPaths.createTaskRequest,
      data: {
        'to_user': toUserId,
        'content': content,
        'post_id': postId,
        'offer_amount': offerAmount.toInt(),
      },
    );
    return response.data;
  }

  @Deprecated('Endpoint not found in API documentation - verify with server team')
  Future<dynamic> getGivenTaskRequestsForPost(String postId) async {
    final response = await _dioService.get(ApiPaths.getGivenTaskRequestsForPost(postId));
    return response.data;
  }
}

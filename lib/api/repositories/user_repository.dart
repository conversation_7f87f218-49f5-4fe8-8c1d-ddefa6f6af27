import 'package:darve/services/http/http_service.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/result.dart';

class UserRepository {
  final HttpService _dioService;

  UserRepository(this._dioService);

  /// Set password using Result type for better error handling
  Future<Result<void>> setPassword(String password) async {
    return ResultHelper.tryCallAsync(
      () async {
        await _dioService.post(
          ApiPaths.setPassword,
          data: {'password': password},
        );
      },
      errorMessage: 'Unable to set password. Please try again.',
      errorCode: 'SET_PASSWORD_ERROR',
      metadata: {
        'operation': 'setPassword',
      },
    );
  }

  /// Change password using Result type for better error handling
  Future<Result<bool>> changePassword(String oldPassword, String newPassword) async {
    return ResultHelper.tryCallAsync(
      () async {
        await _dioService.patch(
          ApiPaths.changePassword,
          data: {
            'old_password': oldPassword,
            'new_password': newPassword,
          },
        );
        return true;
      },
      errorMessage: 'Unable to change password. Please try again.',
      errorCode: 'CHANGE_PASSWORD_ERROR',
      metadata: {
        'operation': 'changePassword',
      },
    );
  }

  /// Start email verification using Result type for better error handling
  Future<Result<bool>> emailVerificationStart(String email) async {
    return ResultHelper.tryCallAsync(
      () async {
        await _dioService.post(
          ApiPaths.emailVerificationStart,
          data: {'email': email},
        );
        return true;
      },
      errorMessage: 'Unable to send verification email. Please try again.',
      errorCode: 'EMAIL_VERIFICATION_START_ERROR',
      metadata: {
        'email': email,
        'operation': 'emailVerificationStart',
      },
    );
  }

  /// Confirm email verification using Result type for better error handling
  Future<Result<bool>> emailVerificationConfirm(String email, String code) async {
    return ResultHelper.tryCallAsync(
      () async {
        await _dioService.post(
          ApiPaths.emailVerificationConfirm,
          data: {
            'email': email,
            'code': code,
          },
        );
        return true;
      },
      errorMessage: 'Unable to verify email. Please try again.',
      errorCode: 'EMAIL_VERIFICATION_CONFIRM_ERROR',
      metadata: {
        'email': email,
        'operation': 'emailVerificationConfirm',
      },
    );
  }
}

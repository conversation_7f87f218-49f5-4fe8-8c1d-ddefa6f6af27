import 'dart:convert';
import 'package:darve/api/models/user_notification_model.dart';
import 'package:darve/services/http/http_service.dart';
import 'package:darve/api/models/notification.dart';
import 'package:darve/services/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:dio/dio.dart';
import 'package:darve/utils/constants.dart';

class GetNotificationsQuery {
  num? start;
  num? count;
  bool? isRead;

  GetNotificationsQuery({this.start, this.count, this.isRead});

  Map<String, String> toMap() {
    final map = <String, String>{};
    if (start != null) map['start'] = start.toString();
    if (count != null) map['count'] = count.toString();
    if (isRead != null) map['is_read'] = isRead.toString();
    return map;
  }
}

/// Notifications API using DioService for HTTP requests
/// Provides methods for fetching, reading notifications and SSE streaming
/// Includes adapter methods to convert UserNotificationModel to DarveNotification
class NotificationRepository {
  final HttpService _httpService;
  late final AuthService _authService;

  NotificationRepository(this._httpService) {
    _authService = AuthProvider.auth;
  }

  Future<List<UserNotificationModel>> get(
      {GetNotificationsQuery? query}) async {
    try {
      final response = await _httpService.get<dynamic>(
        ApiPaths.getNotifications,
        queryParameters: query?.toMap(),
      );

      final responseData = response.data;
      if (responseData is List) {
        return responseData
            .map((item) => UserNotificationModel.fromMap(item as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Invalid response format: expected List, got ${responseData.runtimeType}');
      }
    } on DioException catch (e) {
      throw Exception('Failed to get notifications: ${e.message}');
    }
  }

  Future<void> read(String id) async {
    try {
      await _httpService.post(ApiPaths.readNotification(id));
    } on DioException catch (e) {
      throw Exception('Failed to mark notification as read: ${e.message}');
    }
  }

  Future<void> readAll() async {
    try {
      await _httpService.post(ApiPaths.readAllNotifications);
    } on DioException catch (e) {
      throw Exception('Failed to mark all notifications as read: ${e.message}');
    }
  }
 
  Future<num> getCountOfUnread() async {
    try {
      final response = await _httpService.get<num>(
        ApiPaths.getCountOfUnreadNotifications,
        queryParameters: {'is_read': 'false'},
      );
      return response.data ?? 0;
    } on DioException catch (e) {
      throw Exception('Failed to get unread notifications count: ${e.message}');
    }
  }

  Stream<UserNotificationModel> getSse() async* {
    try {
      final stream = _httpService.getStream(ApiPaths.notificationsSse);

      await for (String? line in stream) {
        if (line == null || line.isEmpty || line == 'heartbeat') continue;

        try {
          final Map<String, dynamic> data = json.decode(line);
          yield UserNotificationModel.fromMap(data);
        } catch (e) {
          // Skip invalid JSON data
          continue;
        }
      }
    } catch (e) {
      throw Exception('Failed to connect to notifications SSE: $e');
    }
  }

  // ============================================================================
  // ADAPTER METHODS - Convert UserNotificationModel to DarveNotification
  // These methods preserve existing business logic and UI components
  // ============================================================================

  /// Adapter method to convert UserNotificationModel to DarveNotification
  /// This preserves existing business logic and UI components
  DarveNotification _adaptUserNotificationToDarveNotification(UserNotificationModel userNotification) {
    String type;
    Object? value;

    // Map UserNotificationEvent enum to DarveNotification string constants
    switch (userNotification.event) {
      case UserNotificationEvent.userFollowAdded:
        type = DarveNotification.userFollowAdded;
        // Extract username from metadata if available
        if (userNotification.metadata != null && userNotification.metadata is Map) {
          final metadata = userNotification.metadata as Map<String, dynamic>;
          value = UserFollowAdded(
            username: metadata['username'] ?? userNotification.createdBy,
            followedUser: metadata['followedUser'] ?? _authService.user?.username ?? '',
          );
        } else {
          value = UserFollowAdded(
            username: userNotification.createdBy,
            followedUser: _authService.user?.username ?? '',
          );
        }
        break;
      case UserNotificationEvent.userTaskRequestCreated:
        type = DarveNotification.userTaskRequestCreated;
        if (userNotification.metadata != null && userNotification.metadata is Map) {
          final metadata = userNotification.metadata as Map<String, dynamic>;
          value = UserTaskRequestCreated(
            taskId: metadata['taskId'] ?? userNotification.id,
            fromUser: metadata['fromUser'] ?? userNotification.createdBy,
            toUser: metadata['toUser'] ?? _authService.user?.id ?? '',
          );
          /// TODO @sj 10/07/2025: may need to use IdExtractor to properly assign id
    // var val = notif['event']['value'];
    // taskId: IdExtractor.getIdent(val, propName: "task_id"),
    // fromUser: IdExtractor.getIdent(val, propName: "from_user"),
    // toUser: IdExtractor.getIdent(val, propName: "to_user"),

        } else {
          value = UserTaskRequestCreated(
            taskId: userNotification.id,
            fromUser: userNotification.createdBy,
            toUser: _authService.user?.id ?? '',
          );
        }
        break;
      case UserNotificationEvent.userChatMessage:
        type = DarveNotification.userChatMessage;
        value = userNotification.metadata;
        break;
      case UserNotificationEvent.userTaskRequestReceived:
        type = DarveNotification.userTaskRequestReceived;
        value = userNotification.metadata;
        break;
      case UserNotificationEvent.userTaskRequestDelivered:
        type = DarveNotification.userTaskRequestDelivered;
        value = userNotification.metadata;
        break;
      case UserNotificationEvent.userCommunityPost:
        type = DarveNotification.userCommunityPost;
        value = userNotification.metadata;
        break;
      case UserNotificationEvent.userBalanceUpdate:
        type = DarveNotification.userBalanceUpdate;
        value = userNotification.metadata;
        break;
      default:
        type = userNotification.event.name;
        value = userNotification.metadata;
        break;
    }

    return DarveNotification.fromDetails(userNotification.id, type, value);
  }

  /// Get notifications as DarveNotification objects (adapted for existing UI)
  Future<List<DarveNotification>> getNotifications({GetNotificationsQuery? query}) async {
    final userNotifications = await get(query: query);
    return userNotifications
        .map((userNotif) => _adaptUserNotificationToDarveNotification(userNotif))
        .toList();
  }

  /// Get SSE stream as DarveNotification objects (adapted for existing UI)
  Stream<DarveNotification> getNotificationsSse() async* {
    await for (final userNotification in getSse()) {
      yield _adaptUserNotificationToDarveNotification(userNotification);
    }
  }

}

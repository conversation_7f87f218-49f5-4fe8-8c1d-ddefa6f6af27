import 'package:darve/api/models/auth_response_model.dart';
import 'package:darve/config.dart';
import 'package:darve/services/http/http_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:darve/utils/constants.dart';

class AuthRepository {
  final HttpService _dioService;

  AuthRepository(this._dioService);

  Future<AuthResponseModel> login(String username, String password) async {
    final response = await _dioService.post(
      ApiPaths.login,
      data: {
        'username': username,
        'password': password,
      },
    );

    return AuthResponseModel.fromJson(response.data);
  }

  Future<AuthResponseModel> register({
    required String username,
    required String password,
    String? email,
    String? name,
    String? imageUri,
  }) async {
    final data = {
      'username': username,
      'password': password,
    };

    if (email != null) data['email'] = email;
    if (name != null) data['full_name'] = name;
    if (imageUri != null) data['image_uri'] = imageUri;

    final response = await _dioService.post(
      ApiPaths.register,
      data: data,
    );

    return AuthResponseModel.fromJson(response.data);
  }

  Future<void> forgotPassword(String emailOrUsername) async {
    await _dioService.post(
      ApiPaths.forgotPassword,
      data: {'email_or_username': emailOrUsername},
    );
  }

  Future<void> resetPassword({
    required String emailOrUsername,
    required String code,
    required String password,
  }) async {
    await _dioService.post(
      ApiPaths.resetPassword,
      data: {
        'email_or_username': emailOrUsername,
        'code': code,
        'passowrd': password, // Note: keeping the typo from original API
      },
    );
  }

  Future<AuthResponseModel?> signWithFacebook() async {
    final result = await FacebookAuth.instance.login();
    if (result.status == LoginStatus.cancelled) return null;

    final accessToken = await FacebookAuth.instance.accessToken;
    if (accessToken == null) {
      throw Exception("Access Token invalid");
    }

    final response = await _dioService.post(
      ApiPaths.signWithFacebook,
      data: {"token": accessToken.token},
    );

    return AuthResponseModel.fromJson(response.data);
  }

  Future<AuthResponseModel> signWithApple() async {
    final credential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );

    final response = await _dioService.post(
      ApiPaths.signWithApple,
      data: {'token': credential.identityToken},
    );

    return AuthResponseModel.fromJson(response.data);
  }

  Future<AuthResponseModel> signWithGoogle() async {
    final scopes = ["email"];
    GoogleSignIn googleSignIn = defaultTargetPlatform == TargetPlatform.iOS
        ? GoogleSignIn(
            scopes: scopes,
            clientId: AppConfig.instance.googleIosClientId,
          )
        : defaultTargetPlatform == TargetPlatform.android
            ? GoogleSignIn(
                scopes: scopes,
                serverClientId: AppConfig.instance.googleAndroidClientId,
              )
            : throw Exception("Platform not supported");

    final account = await googleSignIn.signIn();
    if (account == null) throw Exception('Google sign-in aborted');

    final auth = await account.authentication;
    final response = await _dioService.post(
      ApiPaths.signWithGoogle,
      data: {'token': auth.idToken},
    );

    return AuthResponseModel.fromJson(response.data);
  }
}

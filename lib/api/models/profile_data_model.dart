import 'package:darve/utils/id_extractor.dart';
import 'package:darve/utils/parsers/social_parse.dart';

class ProfileDataModel {
  final String? fullName;
  final String userId;
  final String userName;
  final String? emailId;
  final String? bio;
  final String? imageUri;
  final int followersNr;
  final int followingNr;
  final List<String> platforms;
  final List<String>? socialLinks;

  ProfileDataModel({
    required this.fullName,
    required this.userId,
    required this.userName,
    required this.emailId,
    required this.bio,
    required this.imageUri,
    required this.followersNr,
    required this.followingNr,
    required this.platforms,
    required this.socialLinks,
  });

  factory ProfileDataModel.fromJson(Map<String, dynamic> json) {
    return ProfileDataModel(
      fullName: json['full_name'],
      userId: IdExtractor.getIdent(json, propName: 'user_id'),
      userName: json['username'],
      emailId: json['emailId'],
      bio: json['bio'] ?? "",
      imageUri: json['image_uri'],
      followersNr: json['followers_nr'] ?? 0,
      followingNr: json['following_nr'] ?? 0,
      platforms: SocialParse().parseSocialLinks(json['social_links']),
      socialLinks: json['social_links'] ?? [],
    );
  }
}

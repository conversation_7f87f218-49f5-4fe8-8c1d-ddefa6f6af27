class TransactionModel {
  final String id;
  final String wallet;
  final String type;
  final int amount;
  final bool isOtherUserGateway;
  final String title;
  final DateTime created;

  TransactionModel({
    required this.id,
    required this.wallet,
    required this.type,
    required this.amount,
    required this.isOtherUserGateway,
    required this.title,
    required this.created,
  });

  factory TransactionModel.fromDetails(String id, String wallet, String type,
      int amount, bool isOtherUserGateway, String title, DateTime created) {
    return TransactionModel(
        id: id,
        wallet: wallet,
        type: type,
        amount: amount,
        isOtherUserGateway: isOtherUserGateway,
        title: title,
        created: created);
  }
}

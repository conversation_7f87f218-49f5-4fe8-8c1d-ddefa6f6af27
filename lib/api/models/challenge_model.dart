import 'package:darve/helpers/discussion_helper.dart';

class ChallengeModel {
  final String id;
  final String fromUserId;
  final String toUserId;
  final String requestPostId;
  final String requestText;
  final String createdOn;
  final String updatedOn;
  final String status;
  final List<Participant> participants;

  ChallengeModel({
    required this.id,
    required this.fromUserId,
    required this.toUserId,
    required this.requestPostId,
    required this.requestText,
    required this.createdOn,
    required this.updatedOn,
    required this.status,
    required this.participants,
  });

  factory ChallengeModel.fromJson(Map<String, dynamic> json) {
    List<Participant> participantsList = [];
    try {
      participantsList = (json['participants'] != null &&
              json['participants'].isNotEmpty)
          ? List<Participant>.from((json['participants'] as List)
              .map((participantJson) => Participant.fromJson(participantJson)))
          : [];
    } catch (e) {
      participantsList = [];
    }

    return ChallengeModel(
      id: DiscussionIdHelper().getDiscussionId(json),
      fromUserId: DiscussionIdHelper().getDiscussionId(json["from_user"]),
      toUserId: DiscussionIdHelper().getDiscussionId(json["to_user"]),
      requestPostId:
          DiscussionIdHelper().getDiscussionId(json, propName: "request_post"),
      requestText: json['request_txt'],
      createdOn: json['r_created'],
      updatedOn: json['r_updated'],
      status: json['status'],
      participants: participantsList,
    );
  }
}

class Participant {
  final int amount;
  final String username;
  final String userId;
  final String fullName;

  Participant({
    required this.amount,
    required this.username,
    required this.userId,
    required this.fullName,
  });

  factory Participant.fromJson(Map<String, dynamic> json) {
    return Participant(
      amount: json['amount'],
      username: json['user']['username'],
      fullName: json['user']['full_name'],
      userId: DiscussionIdHelper().getDiscussionId(json['user']),
    );
  }
}

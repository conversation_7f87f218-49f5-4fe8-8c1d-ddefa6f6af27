class ChatListModel {
  String id;
  String? title;
  String? userIds;
  String? avatarUrl;
  String? fullName;
  bool? isOnline;
  String? lastMessage;
  String? userName;
  String? timestamp;
  ChatListModel({
    required this.id,
    this.title,
    this.userIds,
    this.avatarUrl,
    this.fullName,
    this.isOnline,
    this.lastMessage,
    this.userName,
    this.timestamp,
  });
  factory ChatListModel.fromJson(Map<String, dynamic> json) {
    return ChatListModel(
      id: json['id'] as String,
      title: json['title'] as String?,
      userIds: json['userId'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      fullName: json['full_name'] as String?,
      isOnline: json['isOnline'] as bool?,
      lastMessage: json['lastMessage'] as String?,
      userName: json['userName'] as String?,
      timestamp: json['timestamp'] as String?,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ChatListModel && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}

class Chat {
  bool inbound;
  String body;
  int time;
  String type;
  Chat({
    required this.inbound,
    required this.body,
    required this.time,
    required this.type,
  });

  factory Chat.fromJson(Map<String, dynamic> json) {
    return Chat(
      inbound: json['inbound'] as bool,
      body: json['body'] as String,
      time: json['time'] as int,
      type: json['type'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'inbound': inbound,
      'body': body,
      'time': time,
      'type': type,
    };
  }

  @override
  String toString() {
    return 'Chat{inbound: $inbound, body: $body, time: $time, type: $type}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Chat &&
        other.inbound == inbound &&
        other.body == body &&
        other.time == time &&
        other.type == type;
  }

  @override
  int get hashCode {
    return inbound.hashCode ^ body.hashCode ^ time.hashCode ^ type.hashCode;
  }
}

import 'package:darve/api/models/challenge_model.dart';

class ShortenedChallengeModel {
  final String? id;
  final String? avatar;
  final String? username;
  final String? challenge;
  final String? bet;
  final List<Participant>? participants;

  ShortenedChallengeModel({
    required this.id,
    required this.avatar,
    required this.username,
    required this.challenge,
    required this.bet,
    required this.participants,
  });

  factory ShortenedChallengeModel.fromDetails(
      String? id,
      String? avatar,
      String? username,
      String? challenge,
      String? bet,
      List<Participant>? participants) {
    return ShortenedChallengeModel(
        id: id,
        avatar: avatar,
        username: username,
        challenge: challenge,
        bet: bet,
        participants: participants);
  }
}

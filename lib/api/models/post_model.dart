import 'package:darve/helpers/discussion_helper.dart';

class PostModel {
  final String id;
  final String username;
  final String belongsToId;
  final String postTitleUri;
  final String title;
  final String content;
  final List<dynamic>? mediaLinks;
  final String createdOn;
  final int repliesNr;

  PostModel({
    required this.id,
    required this.username,
    required this.belongsToId,
    required this.postTitleUri,
    required this.title,
    required this.content,
    required this.mediaLinks,
    required this.createdOn,
    required this.repliesNr,
  });

  factory PostModel.fromJson(Map<String, dynamic> json) {
    return PostModel(
      id: DiscussionIdHelper().getDiscussionId(json),
      username: json['username'] ?? json['created_by_name'],
      belongsToId:
          DiscussionIdHelper().getDiscussionId(json, propName: "belongs_to_id"),
      postTitleUri: json["r_title_uri"],
      title: json["title"],
      content: json["content"],
      mediaLinks: json["media_links"],
      createdOn: json["r_created"],
      repliesNr: json["replies_nr"],
    );
  }

  factory PostModel.empty() {
    return PostModel(
      id: "",
      username: "",
      belongsToId: "",
      postTitleUri: "",
      title: "",
      content: "",
      mediaLinks: [],
      createdOn: "",
      repliesNr: 0,
    );
  }
}

class UserFollowAdded{
    final String username;
    final String followedUser;

    UserFollowAdded({
      required this.username,
      required this.followedUser
    });
}

class UserTaskRequestCreated{
    final String taskId;
    final String fromUser;
    final String toUser;

    UserTaskRequestCreated({
      required this.taskId,
      required this.fromUser,
      required this.toUser
    });
}


class DarveNotification {
  final String id;
  final String type;
  final Object? value;

  static const String userFollowAdded = "UserFollowAdded";
  static const String userChatMessage = "UserChatMessage";
  static const String userTaskRequestReceived = "UserTaskRequestReceived";
  static const String userTaskRequestDelivered = "UserTaskRequestDelivered";
  static const String userCommunityPost = "UserCommunityPost";
  static const String userTaskRequestCreated = "UserTaskRequestCreated";
  static const String userBalanceUpdate = "UserBalanceUpdate";

  DarveNotification({
    required this.id,
    required this.type,
    this.value
  });

  factory DarveNotification.fromDetails(
    String id,
    String type,
    Object? value
    ) {
    return DarveNotification(
        id: id,
        type:type,
        value: value
        );
  }
}

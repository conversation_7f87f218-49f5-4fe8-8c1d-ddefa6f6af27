import 'package:darve/services/providers/auth_provider.dart';
import 'package:darve/routes/app_routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AuthMiddleware extends GetMiddleware {
  @override
  int? get priority => 1;

  @override
  RouteSettings? redirect(String? route) {
    // Get auth service
    final authService = AuthProvider.auth;
    
    // If not logged in and trying to access a protected route, redirect to login
    if (!authService.isLoggedIn && route != AppRoutes.login) {
      return const RouteSettings(name: AppRoutes.login);
    }
    
    // If logged in and trying to access login page, redirect to home
    if (authService.isLoggedIn && route == AppRoutes.login) {
      return const RouteSettings(name: AppRoutes.home);
    }
    
    // No redirection needed
    return null;
  }
}

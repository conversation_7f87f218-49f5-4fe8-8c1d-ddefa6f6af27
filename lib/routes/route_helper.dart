import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:darve/routes/app_routes.dart';

class RouteHelper {
  // Auth navigation methods
  static void goToLogin() {
    Get.offAllNamed(AppRoutes.login);
  }

  static void goToSignUp() {
    Get.toNamed(AppRoutes.signup);
  }

  static void goToForgotPassword() {
    Get.toNamed(AppRoutes.forgotPassword);
  }

  // Main navigation methods
  static void goToHome() {
    Get.offAllNamed(AppRoutes.home);
  }

  static void goToChat({
    required String chatId,
    required String title,
    required String avatarUrl,
    required String userId,
  }) {
    Get.toNamed(
      AppRoutes.chat,
      arguments: {
        'chatId': chatId,
        'title': title,
        'avatarUrl': avatarUrl,
        'userId': userId,
      },
    );
  }

  // Settings navigation methods
  static void goToSettings() {
    Get.toNamed(AppRoutes.settings);
  }

  static void goToPrivacySettings() {
    Get.toNamed(AppRoutes.privacySettings);
  }

  static void goToChangePassword() {
    Get.toNamed(AppRoutes.changePassword);
  }

  static void goToContactSupport() {
    Get.toNamed(AppRoutes.contactSupport);
  }

  static void goToMoreInfo() {
    Get.toNamed(AppRoutes.moreInfo);
  }

  // Feature navigation methods
  static void goToSearch() {
    Get.toNamed(AppRoutes.search);
  }

  static void goToNotifications() {
    Get.toNamed(AppRoutes.notifications);
  }

  // TODO @sj 17/07/2025: use either of userId or userName optionally imageUrl
  static void goToProfile({
    required String userId,
    required String username,
    required String imageUrl,
  }) {
    Get.toNamed(
      AppRoutes.profile,
      arguments: {
        'userId': userId,
        'username': username,
        'imageUrl': imageUrl,
      },
    );
  }

  static void goToProfileInsights({
    required String userId,
    int initialTab = 0,
  }) {
    Get.toNamed(
      AppRoutes.profileInsights,
      arguments: {
        'userId': userId,
        'initialTab': initialTab,
      },
    );
  }

  // Recording navigation methods
  static void goToMyChallenges() {
    Get.toNamed(AppRoutes.myChallenges);
  }

  static void goToCamera({
    required dynamic cameras,
    required String taskId,
    required VoidCallback callback,
  }) {
    Get.toNamed(
      AppRoutes.camera,
      arguments: {
        'cameras': cameras,
        'taskId': taskId,
        'callback': callback,
      },
    );
  }

  // Navigation utilities
  static void goBack() {
    Get.back();
  }

  // Check current route
  static bool get isOnLoginPage => Get.currentRoute == AppRoutes.login;
  static bool get isOnHomePage => Get.currentRoute == AppRoutes.home;
  static bool get isOnChatPage => Get.currentRoute == AppRoutes.chat;
}

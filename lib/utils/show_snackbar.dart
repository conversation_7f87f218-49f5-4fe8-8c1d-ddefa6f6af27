import 'package:darve/utils/server_assets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

class SnackbarHelper {
  static void showFollowSnackbar({
    BuildContext? context,
    required String imageUri,
    required String username,
    required Color bgColor,
    required bool isFollowed,
    int duration = 1200,
  }) {
    ScaffoldMessenger.of(context ?? Get.context!).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(50.0),
              child: Image.network(
                ServerAssets().getAssetUrl(imageUri),
                width: 40,
                height: 40,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(width: 10),
            Expanded(
              child: Text(
                '${isFollowed ? "Followed" : "Unfollowed"} $username',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: bgColor,
        duration: Duration(milliseconds: duration),
      ),
    );
  }

  static void showChallengeSnackbar({
    BuildContext? context,
    required String content,
    required Color bgColor,
    int duration = 1200,
  }) {
    ScaffoldMessenger.of(context ?? Get.context!).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Expanded(
              child: Text(
                content,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: bgColor,
        duration: Duration(milliseconds: duration),
      ),
    );
  }
}

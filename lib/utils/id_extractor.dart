import 'package:flutter/material.dart';

class IdExtractor {
  static String getIdent(dynamic obj, {String? propName}) {
    try {
      if (propName != null && obj.containsKey(propName)) {
        return "${obj[propName]['tb']}:${obj[propName]['id']['String']}";
      }
      return "${obj['id']['tb']}:${obj['id']['id']['String']}";
    } catch (e) {
      debugPrint("IdExtractor.getId obj=$obj error===$e");
      return "";
    }
  }
}

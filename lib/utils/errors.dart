import 'package:darve/main.dart';
import 'package:darve/services/error/error_models.dart';
import 'package:darve/services/error/error_reporting_service.dart';
import 'package:flutter/material.dart';

class ErrorsHandle {
  static final ErrorReportingService _errorReporting = ErrorReportingService.instance;

  /// Display error to user with enhanced error handling
  Future displayErrorToast(dynamic error, [String? functionName]) async{
    debugPrint("ERR===$error functionName==$functionName");

    final context = navigatorKey.currentContext;
    if (context == null) {
      debugPrint("❌ No context available for error display");
      return;
    }

    String errorMessage;
    Color backgroundColor = Colors.red;
    IconData icon = Icons.error;

    // Handle AppError objects
    if (error is AppError) {
      errorMessage = _getAppErrorMessage(error);
      backgroundColor = _getErrorColor(error.severity);
      icon = _getErrorIcon(error.severity);

      // Add breadcrumb for user-facing error
      _errorReporting.addBreadcrumb(
        message: 'Error displayed to user: ${error.code ?? error.runtimeType}',
        category: 'ui.error',
        data: {
          'error_message': errorMessage,
          'error_code': error.code,
          'severity': error.severity.name,
          'function_name': functionName,
        },
      );
    } else {
      // Legacy error handling for backward compatibility
      errorMessage = _getLegacyErrorMessage(error, functionName);

      // Report unexpected legacy errors
      _errorReporting.reportException(
        error,
        context: 'legacy_error_display',
        extra: {'function_name': functionName},
      );
    }

    _showErrorSnackBar(context, errorMessage, backgroundColor, icon);
  }

  /// Get user-friendly message from AppError
  String _getAppErrorMessage(AppError error) {
    if (error is ApiError) {
      // For API errors, use the display message which handles both field and simple errors
      return error.displayMessage;
    }

    return error.message;
  }

  /// Get error color based on severity
  Color _getErrorColor(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.expected:
        return Colors.orange; // Warning color for expected errors
      case ErrorSeverity.warning:
        return Colors.amber;
      case ErrorSeverity.error:
        return Colors.red;
      case ErrorSeverity.fatal:
        return Colors.red.shade900;
    }
  }

  /// Get error icon based on severity
  IconData _getErrorIcon(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.expected:
        return Icons.info;
      case ErrorSeverity.warning:
        return Icons.warning;
      case ErrorSeverity.error:
        return Icons.error;
      case ErrorSeverity.fatal:
        return Icons.dangerous;
    }
  }

  /// Legacy error message handling for backward compatibility
  String _getLegacyErrorMessage(dynamic error, String? functionName) {
    return functionName == "followUser"
        ? "Already following the user!"
        : functionName == "urlLaunchError"
            ? error.toString()
            : error.toString().contains("The provided JWT")
                ? "Session Expired! Log in again"
                : error.toString().contains("Min 5 characters")
                    ? "Content length too short, need atleast 5 characters"
                    : error.toString();
  }

  /// Show error snackbar with enhanced styling
  void _showErrorSnackBar(BuildContext context, String message, Color backgroundColor, IconData icon) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: DefaultTextStyle(
                style: const TextStyle(fontSize: 12.0, color: Colors.white),
                child: Text(message),
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.only(top: 10, left: 10, bottom: 10, right: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: const Duration(seconds: 3), // Slightly longer for validation errors
        action: message.length > 100 ? SnackBarAction(
          label: 'Details',
          textColor: Colors.white,
          onPressed: () => _showErrorDialog(context, message),
        ) : null,
      ),
    );
  }

  /// Show detailed error dialog for long messages
  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('Error Details'),
          ],
        ),
        content: SingleChildScrollView(
          child: Text(message),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Display success message
  void displaySuccessToast(String message) {
    final context = navigatorKey.currentContext;
    if (context == null) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: DefaultTextStyle(
                style: const TextStyle(fontSize: 12.0, color: Colors.white),
                child: Text(message),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.only(top: 10, left: 10, bottom: 10, right: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Display info message
  void displayInfoToast(String message) {
    final context = navigatorKey.currentContext;
    if (context == null) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: DefaultTextStyle(
                style: const TextStyle(fontSize: 12.0, color: Colors.white),
                child: Text(message),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.only(top: 10, left: 10, bottom: 10, right: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

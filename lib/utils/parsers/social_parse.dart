class SocialParse{
List<String> parseSocialLinks(List<String>? socialLinks) {
  if(socialLinks==null)return [];
  List<String> platforms = [];

  for (var link in socialLinks) {
    if (link.contains('instagram.com')) {
      platforms.add('Instagram');
    } else if (link.contains('facebook.com')) {
      platforms.add('Facebook');
    } else if (link.contains('twitter.com') || link.contains('x.com')) {
      platforms.add('X');
    } else if (link.contains('youtube.com')) {
      platforms.add('YouTube');
    }
  }

  return platforms;
}

}
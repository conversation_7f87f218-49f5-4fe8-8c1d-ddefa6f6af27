import 'package:dartz/dartz.dart';
import 'package:darve/services/error/error_models.dart';
import 'package:darve/ui/core/entity_state_widget.dart';

/// Type alias for Result using dartz Either
/// Left = Error, Right = Success
typedef Result<T> = Either<AppError, T>;

/// Extension methods for Result to make it easier to work with
extension ResultExtensions<T> on Result<T> {
  /// Check if the result is a success
  bool get isSuccess => isRight();

  /// Check if the result is an error
  bool get isError => isLeft();

  /// Get the success value or null
  T? get data => fold((error) => null, (data) => data);

  /// Get the error or null
  AppError? get error => fold((error) => error, (data) => null);

  /// Get the success value or throw the error
  T get dataOrThrow => fold((error) => throw error, (data) => data);

  /// Transform the success value
  Result<U> map<U>(U Function(T) mapper) {
    return fold(
      (error) => Left(error),
      (data) => Right(mapper(data)),
    );
  }

  /// Transform the error
  Result<T> mapError(AppError Function(AppError) mapper) {
    return fold(
      (error) => Left(mapper(error)),
      (data) => Right(data),
    );
  }

  /// Execute a function if the result is a success
  Result<T> onSuccess(void Function(T) action) {
    return fold(
      (error) => Left(error),
      (data) {
        action(data);
        return Right(data);
      },
    );
  }

  /// Execute a function if the result is an error
  Result<T> onError(void Function(AppError) action) {
    return fold(
      (error) {
        action(error);
        return Left(error);
      },
      (data) => Right(data),
    );
  }

  /// Convert Result to ViewModel with side effects
  /// This is the standard pattern for converting Result to ViewModel states
  /// Also provides easy callbacks for error and success to display snackbar messages
  ViewModel<T> toViewModel({
    void Function(AppError)? onError,
    void Function(T)? onSuccess,
  }) {
    return fold(
      (error) {
        onError?.call(error);
        return ViewModel<T>.error(error);
      },
      (data) {
        onSuccess?.call(data);
        if (data is List && (data as List).isEmpty) {
          return const ViewModel.empty();
        } else {
          return ViewModel<T>.content(data);
        }
      },
    );
  }

  /// Convert Result to ViewModel for List types with empty state handling
  ViewModel<List<E>> toListViewModel<E>({
    void Function(AppError)? onError,
    void Function(List<E>)? onSuccess,
  }) {
    return fold(
      (error) {
        onError?.call(error);
        return ViewModel<List<E>>.error(error);
      },
      (data) {
        onSuccess?.call(data as List<E>);
        if ((data as List<E>).isEmpty) {
          return ViewModel<List<E>>.empty();
        } else {
          return ViewModel<List<E>>.content(data as List<E>);
        }
      },
    );
  }
}

/// Helper functions to create Results
class ResultHelper {
  /// Create a success result
  static Result<T> success<T>(T data) => Right(data);

  /// Create an error result
  static Result<T> error<T>(AppError error) => Left(error);

  /// Create an error result from exception
  static Result<T> fromException<T>(
    dynamic exception, {
    String? message,
    String? code,
    Map<String, dynamic>? metadata,
  }) {
    if (exception is AppError) {
      return Left(exception);
    }

    return Left(UserError(
      message: message ?? 'An unexpected error occurred',
      code: code ?? 'UNKNOWN_ERROR',
      originalError: exception,
      metadata: metadata,
    ));
  }

  /// Wrap a function that might throw in a Result
  static Result<T> tryCall<T>(T Function() function, {
    String? errorMessage,
    String? errorCode,
    Map<String, dynamic>? metadata,
  }) {
    try {
      return success(function());
    } catch (e) {
      return fromException(
        e,
        message: errorMessage,
        code: errorCode,
        metadata: metadata,
      );
    }
  }

  /// Wrap an async function that might throw in a Result
  static Future<Result<T>> tryCallAsync<T>(Future<T> Function() function, {
    String? errorMessage,
    String? errorCode,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final result = await function();
      return success(result);
    } catch (e) {
      return fromException(
        e,
        message: errorMessage,
        code: errorCode,
        metadata: metadata,
      );
    }
  }
}
